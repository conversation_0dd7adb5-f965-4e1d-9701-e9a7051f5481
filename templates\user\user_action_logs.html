{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "User Action Logs" %} | Legend Fitness Club{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex justify-between items-center mb-4">
            <div>
                <h3 class="text-2xl font-bold">{% trans "User Action Logs" %}</h3>
                <p class="text-gray-600 text-sm mt-1">{% trans "Comprehensive security audit log of all user actions across the system" %}</p>
            </div>
            <a href="{% url 'user:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                <i class="fas fa-arrow-left mr-2"></i>{% trans "Back to Users" %}
            </a>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <i class="fas fa-list text-blue-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{% trans "Total Logs" %}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ total_logs|default:0 }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 rounded-lg">
                        <i class="fas fa-check-circle text-green-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{% trans "Success Rate" %}</p>
                        <p class="text-lg font-semibold text-gray-900">95%</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-yellow-100 rounded-lg">
                        <i class="fas fa-users text-yellow-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{% trans "Active Users" %}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ all_users.count }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center">
                    <div class="p-2 bg-red-100 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-600">{% trans "Critical Actions" %}</p>
                        <p class="text-lg font-semibold text-gray-900">{{ logs|length }}</p>
                    </div>
                </div>
            </div>
        </div>



        <!-- Active Filters Display -->
        {% if selected_action_type or selected_module or selected_user_id or selected_status or search_query or date_from or date_to %}
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h5 class="text-sm font-semibold text-blue-800 mb-2">{% trans "Active Filters:" %}</h5>
            <div class="flex flex-wrap gap-2">
                {% if selected_action_type %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {% trans "Action Type" %}: {{ selected_action_type|title }}
                        <a href="?{% if selected_module %}module={{ selected_module }}&{% endif %}{% if selected_user_id %}user_id={{ selected_user_id }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}{% endif %}" class="ml-1 text-blue-600 hover:text-blue-800">×</a>
                    </span>
                {% endif %}
                {% if selected_module %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {% trans "Module" %}: {{ selected_module|title }}
                        <a href="?{% if selected_action_type %}action_type={{ selected_action_type }}&{% endif %}{% if selected_user_id %}user_id={{ selected_user_id }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}{% endif %}" class="ml-1 text-green-600 hover:text-green-800">×</a>
                    </span>
                {% endif %}
                {% if selected_status %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        {% trans "Status" %}: {{ selected_status|title }}
                        <a href="?{% if selected_action_type %}action_type={{ selected_action_type }}&{% endif %}{% if selected_module %}module={{ selected_module }}&{% endif %}{% if selected_user_id %}user_id={{ selected_user_id }}&{% endif %}{% if search_query %}search={{ search_query }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}{% endif %}" class="ml-1 text-yellow-600 hover:text-yellow-800">×</a>
                    </span>
                {% endif %}
                {% if search_query %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        {% trans "Search" %}: "{{ search_query }}"
                        <a href="?{% if selected_action_type %}action_type={{ selected_action_type }}&{% endif %}{% if selected_module %}module={{ selected_module }}&{% endif %}{% if selected_user_id %}user_id={{ selected_user_id }}&{% endif %}{% if selected_status %}status={{ selected_status }}&{% endif %}{% if date_from %}date_from={{ date_from }}&{% endif %}{% if date_to %}date_to={{ date_to }}{% endif %}" class="ml-1 text-purple-600 hover:text-purple-800">×</a>
                    </span>
                {% endif %}
            </div>
            <p class="text-xs text-blue-600 mt-2">{% trans "Showing" %} {{ total_logs }} {% trans "filtered results" %}</p>
        </div>
        {% endif %}

        <!-- Filters Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h4 class="text-lg font-semibold mb-4">{% trans "Filter Logs" %}</h4>
            <form method="get" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Search" %}</label>
                        <input type="text" id="search" name="search" value="{{ search_query }}"
                               placeholder="{% trans 'Search description, user...' %}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- User Filter -->
                    <div>
                        <label for="user_id" class="block text-sm font-medium text-gray-700 mb-1">{% trans "User" %}</label>
                        <select id="user_id" name="user_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">{% trans "All Users" %}</option>
                            {% for user in all_users %}
                                <option value="{{ user.id }}" {% if user.id|stringformat:"s" == selected_user_id %}selected{% endif %}>
                                    {{ user.username }} - {{ user.name|default:user.username }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Action Type Filter -->
                    <div>
                        <label for="action_type" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Action Type" %}</label>
                        <select id="action_type" name="action_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">{% trans "All Actions" %}</option>
                            {% for action_code, action_name in action_types %}
                                <option value="{{ action_code }}" {% if action_code == selected_action_type %}selected{% endif %}>
                                    {{ action_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Module Filter -->
                    <div>
                        <label for="module" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Module" %}</label>
                        <select id="module" name="module" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">{% trans "All Modules" %}</option>
                            {% for module_code, module_name in modules %}
                                <option value="{{ module_code }}" {% if module_code == selected_module %}selected{% endif %}>
                                    {{ module_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Status" %}</label>
                        <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">{% trans "All Status" %}</option>
                            {% for status_code, status_name in status_choices %}
                                <option value="{{ status_code }}" {% if status_code == selected_status %}selected{% endif %}>
                                    {{ status_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Date From -->
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Date From" %}</label>
                        <input type="date" id="date_from" name="date_from" value="{{ date_from }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <!-- Date To -->
                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">{% trans "Date To" %}</label>
                        <input type="date" id="date_to" name="date_to" value="{{ date_to }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-search mr-2"></i>{% trans "Apply Filters" %}
                    </button>
                    <a href="{% url 'user:user_action_logs' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-times mr-2"></i>{% trans "Clear Filters" %}
                    </a>
                </div>
            </form>
        </div>

        <!-- Logs Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h4 class="text-lg font-semibold">{% trans "Action Log Entries" %}</h4>
                {% if page_obj.paginator.count %}
                    <p class="text-sm text-gray-600 mt-1">
                        {% trans "Showing" %} {{ page_obj.start_index }} - {{ page_obj.end_index }} {% trans "of" %} {{ page_obj.paginator.count }} {% trans "entries" %}
                    </p>
                {% endif %}
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Time" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "User" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Action" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Module" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Status" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Target" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "Description" %}</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">{% trans "IP Address" %}</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for log in page_obj %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ log.action_time|date:"Y-m-d H:i:s" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ log.user.username|default:"Unknown" }}
                                    </div>
                                    {% if log.user.name %}
                                        <div class="text-sm text-gray-500 ml-1">
                                            ({{ log.user.name }})
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if 'delete' in log.action_type %}bg-red-100 text-red-800
                                    {% elif 'edit' in log.action_type %}bg-yellow-100 text-yellow-800
                                    {% elif 'create' in log.action_type %}bg-green-100 text-green-800
                                    {% else %}bg-blue-100 text-blue-800{% endif %}">
                                    {{ log.get_action_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ log.get_module_display }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if log.status == 'success' %}bg-green-100 text-green-800
                                    {% elif log.status == 'failed' %}bg-red-100 text-red-800
                                    {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    {{ log.get_status_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {% if log.target_description %}
                                    <div class="max-w-xs truncate" title="{{ log.target_description }}">
                                        {{ log.target_description }}
                                    </div>
                                {% elif log.target_model and log.target_id %}
                                    {{ log.target_model }} #{{ log.target_id }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">
                                {% if log.description %}
                                    <div class="max-w-xs truncate" title="{{ log.description }}">
                                        {{ log.description }}
                                    </div>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ log.ip_address|default:"-" }}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                                <div class="flex flex-col items-center justify-center py-8">
                                    <i class="fas fa-search text-4xl text-gray-300 mb-4"></i>
                                    <p class="text-lg font-medium">{% trans "No logs found" %}</p>
                                    <p class="text-sm">{% trans "Try adjusting your filters or search criteria" %}</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Enhanced Pagination -->
            <div class="bg-white border-t border-gray-200">
                <!-- Mobile Pagination (Simple Previous/Next) -->
                <div class="flex justify-between items-center px-4 py-3 sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-chevron-left mr-2"></i>{% trans "Previous" %}
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            <i class="fas fa-chevron-left mr-2"></i>{% trans "Previous" %}
                        </span>
                    {% endif %}

                    <span class="text-sm text-gray-700">
                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                    </span>

                    {% if page_obj.has_next %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            {% trans "Next" %}<i class="fas fa-chevron-right ml-2"></i>
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            {% trans "Next" %}<i class="fas fa-chevron-right ml-2"></i>
                        </span>
                    {% endif %}
                </div>

                <!-- Desktop Pagination (Full Controls) -->
                <div class="hidden sm:flex sm:flex-col lg:flex-row lg:items-center lg:justify-between px-6 py-4 space-y-4 lg:space-y-0">
                    <!-- Left side: Results info and items per page -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                        <div>
                            <p class="text-sm text-gray-700">
                                {% trans "Showing" %} <span class="font-medium">{{ page_obj.start_index }}</span> {% trans "to" %} <span class="font-medium">{{ page_obj.end_index }}</span> {% trans "of" %} <span class="font-medium">{{ page_obj.paginator.count }}</span> {% trans "entries" %}
                            </p>
                        </div>
                        <div class="flex items-center">
                            <label for="items-per-page" class="text-sm text-gray-700 mr-2">{% trans "Items per page" %}:</label>
                            <select id="items-per-page" name="items_per_page" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500" onchange="changeItemsPerPage()">
                                <option value="5" {% if items_per_page == 5 %}selected{% endif %}>5</option>
                                <option value="10" {% if items_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="25" {% if items_per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if items_per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if items_per_page == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>
                    </div>

                    <!-- Right side: Pagination controls -->
                    {% if page_obj.has_other_pages %}
                    <div class="flex items-center space-x-2">
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <!-- First Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page=1"
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="{% trans 'First page' %}">
                                    <span class="sr-only">{% trans "First" %}</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">{% trans "First" %}</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </span>
                            {% endif %}

                            <!-- Previous Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">{% trans "Previous" %}</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">{% trans "Previous" %}</span>
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            {% endif %}

                            <!-- Page Numbers -->
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span aria-current="page" class="relative z-10 inline-flex items-center bg-blue-900 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-900">{{ num }}</span>
                                {% elif num > page_obj.number|add:"-3" and num < page_obj.number|add:"3" %}
                                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                       class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                {% elif num == 1 or num == page_obj.paginator.num_pages %}
                                    {% if num != page_obj.number %}
                                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                           class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                    {% endif %}
                                {% elif num == page_obj.number|add:"-4" or num == page_obj.number|add:"4" %}
                                    <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                                {% endif %}
                            {% endfor %}

                            <!-- Next Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">{% trans "Next" %}</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">{% trans "Next" %}</span>
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            {% endif %}

                            <!-- Last Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.paginator.num_pages }}"
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="{% trans 'Last page' %}">
                                    <span class="sr-only">{% trans "Last" %}</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">{% trans "Last" %}</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </span>
                            {% endif %}
                        </nav>

                        <!-- Jump to Page -->
                        <div class="flex items-center ml-4">
                            <span class="text-sm text-gray-700 mr-2">{% trans "Go to page" %}:</span>
                            <input type="number" id="jump-to-page" min="1" max="{{ page_obj.paginator.num_pages }}" value="{{ page_obj.number }}"
                                   class="w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500">
                            <button id="jump-page-btn" class="ml-2 bg-blue-900 text-white px-2 py-1 rounded text-sm hover:bg-blue-800 transition duration-200">{% trans "Go" %}</button>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock body %}

{% block js %}
<script>
    // Auto-submit form when filters change (optional)
    document.addEventListener('DOMContentLoaded', function() {
        const filterForm = document.querySelector('form');
        const selectElements = filterForm.querySelectorAll('select');

        selectElements.forEach(select => {
            select.addEventListener('change', function() {
                // Optional: Auto-submit on filter change
                // filterForm.submit();
            });
        });

        // Items per page functionality
        const itemsPerPageSelect = document.getElementById('items-per-page');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', function() {
                changeItemsPerPage();
            });
        }

        // Jump to page functionality
        const jumpToPageInput = document.getElementById('jump-to-page');
        const jumpPageBtn = document.getElementById('jump-page-btn');

        if (jumpToPageInput && jumpPageBtn) {
            jumpPageBtn.addEventListener('click', function() {
                const pageNum = parseInt(jumpToPageInput.value);
                const maxPage = parseInt(jumpToPageInput.getAttribute('max'));

                if (pageNum && pageNum > 0 && pageNum <= maxPage) {
                    // Build the URL with the current query parameters
                    let url = new URL(window.location.href);
                    let params = new URLSearchParams(url.search);

                    // Update or add the page parameter
                    params.set('page', pageNum);

                    // Redirect to the new URL
                    window.location.href = `${url.pathname}?${params.toString()}`;
                } else {
                    alert(`{% trans "Please enter a valid page number between 1 and" %} ${maxPage}`);
                    jumpToPageInput.value = "{{ page_obj.number }}";
                }
            });

            // Allow Enter key to trigger jump
            jumpToPageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    jumpPageBtn.click();
                }
            });
        }
    });

    // Function to change items per page
    function changeItemsPerPage() {
        const itemsPerPage = document.getElementById('items-per-page').value;

        // Build the URL with current query parameters
        let url = new URL(window.location.href);
        let params = new URLSearchParams(url.search);

        // Update items per page and reset to page 1
        params.set('items_per_page', itemsPerPage);
        params.set('page', '1');

        // Redirect to the new URL
        window.location.href = `${url.pathname}?${params.toString()}`;
    }
</script>
{% endblock js %}
