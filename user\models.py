from django.db import models
from django.contrib.auth.models import AbstractUser, UserManager
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
import uuid


class GymUserManager(UserManager):
    """Custom user manager for the gym management system."""

    def create_superuser(self, username, email=None, password=None, **extra_fields):
        """Create and save a superuser with the given username, email, and password."""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_manager', False)  # Manager role has been removed
        extra_fields.setdefault('role', 'admin')
        extra_fields.setdefault('name', 'Administrator')
        extra_fields.setdefault('is_employee', True)  # Set is_employee=True for superusers
        extra_fields.setdefault('email_verified', True)  # Superusers have verified emails by default

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self._create_user(username, email, password, **extra_fields)

GENDER_CHOICES = [
    ('male', 'Male'),
    ('female', 'Female'),
]

ROLE_CHOICES = [
    ('admin', 'Admin'),
    ('cashier', 'Cashier'),
    ('coach', 'Coach'),
    ('cleaner', 'Cleaner'),
    ('security', 'Security Guard')
]

# Create your models here.
class User(AbstractUser):
    # Use our custom manager
    objects = GymUserManager()

    # Basic information
    phone = models.CharField(_("Phone"), unique=True, max_length=14, blank=True, null=True)
    name = models.CharField(_("Full Name"), max_length=50, null=True, blank=True)
    dob = models.DateField(_("Date of Birth"), null=True, blank=True)
    gender = models.CharField(_("Gender"), max_length=50, null=True, blank=True, choices=GENDER_CHOICES)

    # Employee information
    emp_id = models.CharField(_("Employee ID"), max_length=20, null=True, blank=True)
    nid = models.CharField(_("NID"), max_length=20, null=True, blank=True)
    is_manager = models.BooleanField(_("Manager"), default=True)
    is_employee = models.BooleanField(_("Employee"), default=False)
    salary = models.IntegerField(_("Salary"), blank=True, null=True)
    due = models.IntegerField(_("Due Salary"), default=0)

    # Contact and address
    address = models.TextField(_("Address"), null=True, blank=True)

    # Profile and role
    photo = models.CharField(_("Photo"), max_length=255, null=True, blank=True)
    role = models.CharField(_("Role"), max_length=20, null=True, blank=True, choices=ROLE_CHOICES)

    # Dates
    join_date = models.DateField(_("Join Date"), null=True, blank=True)
    create_day = models.DateTimeField(_("Create Date"), auto_now_add=True)
    last_activity = models.DateTimeField(_("Last Activity"), null=True, blank=True)

    # Status
    status = models.BooleanField(_("Active Status"), default=True)

    # Enhanced user management fields
    email_verified = models.BooleanField(_("Email Verified"), default=False)
    password_reset_token = models.CharField(_("Password Reset Token"), max_length=100, null=True, blank=True)
    password_reset_expires = models.DateTimeField(_("Password Reset Expires"), null=True, blank=True)

    # Notification preferences
    receive_email_notifications = models.BooleanField(_("Receive Email Notifications"), default=True)
    receive_system_notifications = models.BooleanField(_("Receive System Notifications"), default=True)

    def __str__(self):
        return f'{self.username}'

    def update_last_activity(self):
        """Update the last activity timestamp for the user."""
        self.last_activity = timezone.now()
        self.save(update_fields=['last_activity'])

    def generate_password_reset_token(self):
        """Generate a unique token for password reset."""
        token = str(uuid.uuid4())
        self.password_reset_token = token
        self.password_reset_expires = timezone.now() + timezone.timedelta(hours=24)
        self.save(update_fields=['password_reset_token', 'password_reset_expires'])
        return token

    def clear_password_reset_token(self):
        """Clear the password reset token after it's been used."""
        self.password_reset_token = None
        self.password_reset_expires = None
        self.save(update_fields=['password_reset_token', 'password_reset_expires'])

    def is_password_reset_token_valid(self, token):
        """Check if the provided password reset token is valid."""
        if not self.password_reset_token or not self.password_reset_expires:
            return False
        if self.password_reset_token != token:
            return False
        if timezone.now() > self.password_reset_expires:
            return False
        return True

    def get_role_display_name(self):
        """Get the display name for the user's role."""
        if not self.role:
            return "No Role"
        for role_value, role_name in ROLE_CHOICES:
            if self.role == role_value:
                return role_name
        return self.role.capitalize()

    def has_module_access(self, module_name):
        """Check if the user has access to a specific module based on their role."""
        if self.is_superuser:
            return True

        # Admin role always has access to everything
        if self.role == 'admin':
            return True

        # Use the RolePermission system to check access
        from settings.models import RolePermission
        permission_level = RolePermission.get_module_permission(self.role, module_name)

        # Return True if the user has any level of access (view, edit, full)
        # Return False if the user has no access (none)
        return permission_level != 'none'

class MetaData(models.Model):
    lastChecked = models.DateField(_("Last Checked"),null=True,blank=True)
    funds = models.IntegerField(_("Funds"),null=True,blank=True,default=0)

    # Product auto-deactivation settings
    auto_deactivate_out_of_stock = models.BooleanField(_("Auto-deactivate Out of Stock Products"), default=True,
                                                     help_text=_("Automatically deactivate products when they go out of stock"))
    auto_reactivate_in_stock = models.BooleanField(_("Auto-reactivate In Stock Products"), default=True,
                                                 help_text=_("Automatically reactivate products when they come back in stock"))

    # Pagination settings
    default_items_per_page = models.IntegerField(_("Default Items Per Page"), default=10,
                                               help_text=_("Default number of items to show per page in product list"))

    def __str__(self) :
        return f'{self.lastChecked}'


# Admin Action Log to track critical admin actions
class AdminActionLog(models.Model):
    ACTION_TYPES = [
        ('create_user', 'Create User'),
        ('delete_user', 'Delete User'),
        ('deactivate_user', 'Deactivate User'),
        ('activate_user', 'Activate User'),
        ('edit_user', 'Edit User'),
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('password_change', 'Password Change'),
        ('settings_change', 'Settings Change'),
        ('other', 'Other Action')
    ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='admin_actions',
                            verbose_name=_("Admin User"))
    action_type = models.CharField(_("Action Type"), max_length=50, choices=ACTION_TYPES)
    action_time = models.DateTimeField(_("Action Time"), auto_now_add=True)
    ip_address = models.GenericIPAddressField(_("IP Address"), null=True, blank=True)
    target_user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='target_actions', verbose_name=_("Target User"))
    description = models.TextField(_("Description"), blank=True)

    class Meta:
        verbose_name = _("Admin Action Log")
        verbose_name_plural = _("Admin Action Logs")
        ordering = ['-action_time']

    def __str__(self):
        return f"{self.user.username} - {self.get_action_type_display()} - {self.action_time.strftime('%Y-%m-%d %H:%M:%S')}"

    @classmethod
    def log_action(cls, user, action_type, ip_address=None, target_user=None, description=''):
        """
        Create a log entry for an admin action
        """
        return cls.objects.create(
            user=user,
            action_type=action_type,
            ip_address=ip_address,
            target_user=target_user,
            description=description
        )


# User Action Log to track all user actions across the system
class UserActionLog(models.Model):
    ACTION_TYPES = [
        # Authentication actions
        ('login', 'Login'),
        ('logout', 'Logout'),
        ('failed_login', 'Failed Login'),

        # User management actions
        ('create_user', 'Create User'),
        ('create_employee', 'Create Employee'),
        ('create_systemuser', 'Create System User'),
        ('create_adminuser', 'Create Admin User'),
        ('edit_user', 'Edit User'),
        ('edit_employee', 'Edit Employee'),
        ('edit_systemuser', 'Edit System User'),
        ('edit_adminuser', 'Edit Admin User'),
        ('delete_user', 'Delete User'),
        ('activate_user', 'Activate User'),
        ('deactivate_user', 'Deactivate User'),
        ('password_change', 'Password Change'),

        # Member management actions
        ('create_member', 'Create Member'),
        ('edit_member', 'Edit Member'),
        ('delete_member', 'Delete Member'),
        ('activate_member', 'Activate Member'),
        ('deactivate_member', 'Deactivate Member'),

        # Package management actions
        ('create_package', 'Create Package'),
        ('edit_package', 'Edit Package'),
        ('delete_package', 'Delete Package'),

        # Payment actions
        ('create_payment', 'Create Payment'),
        ('edit_payment', 'Edit Payment'),
        ('delete_payment', 'Delete Payment'),

        # Product management actions
        ('create_product', 'Create Product'),
        ('edit_product', 'Edit Product'),
        ('delete_product', 'Delete Product'),
        ('create_category', 'Create Category'),
        ('edit_category', 'Edit Category'),
        ('delete_category', 'Delete Category'),
        ('create_supplier', 'Create Supplier'),
        ('edit_supplier', 'Edit Supplier'),
        ('delete_supplier', 'Delete Supplier'),
        ('create_purchase', 'Create Purchase'),
        ('edit_purchase', 'Edit Purchase'),
        ('delete_purchase', 'Delete Purchase'),
        ('create_sale', 'Create Sale'),
        ('edit_sale', 'Edit Sale'),
        ('delete_sale', 'Delete Sale'),

        # Pay-per-visit actions
        ('create_paypervisit', 'Create Pay-per-visit'),
        ('edit_paypervisit', 'Edit Pay-per-visit'),
        ('delete_paypervisit', 'Delete Pay-per-visit'),

        # Finance actions
        ('create_transaction', 'Create Finance Transaction'),
        ('edit_transaction', 'Edit Finance Transaction'),
        ('delete_transaction', 'Delete Finance Transaction'),
        ('create_finance_transaction', 'Create Finance Transaction'),
        ('edit_finance_transaction', 'Edit Finance Transaction'),
        ('delete_finance_transaction', 'Delete Finance Transaction'),

        # Payroll actions
        ('create_salary_payment', 'Create Salary Payment'),
        ('edit_salary_payment', 'Edit Salary Payment'),
        ('delete_salary_payment', 'Delete Salary Payment'),
        ('create_salarypayment', 'Create Salary Payment'),
        ('edit_salarypayment', 'Edit Salary Payment'),
        ('delete_salarypayment', 'Delete Salary Payment'),

        # Bill management actions
        ('create_bill', 'Create Bill'),
        ('edit_bill', 'Edit Bill'),
        ('delete_bill', 'Delete Bill'),

        # Template actions
        ('create_template', 'Create Template'),
        ('edit_template', 'Edit Template'),
        ('delete_template', 'Delete Template'),
        ('create_paymenttemplate', 'Create Payment Template'),
        ('edit_paymenttemplate', 'Edit Payment Template'),
        ('delete_paymenttemplate', 'Delete Payment Template'),

        # System actions
        ('change_permissions', 'Change Permissions'),
        ('change_role', 'Change Role'),
        ('export_data', 'Export Data'),
        ('generate_report', 'Generate Report'),
        ('settings_change', 'Settings Change'),
        ('template_change', 'Template Change'),
        ('bulk_delete', 'Bulk Delete'),
        ('bulk_edit', 'Bulk Edit'),
        ('clean_data', 'Clean Data'),
        ('other', 'Other Action'),
    ]

    MODULE_CHOICES = [
        ('user', 'User Management'),
        ('member', 'Member Management'),
        ('payment', 'Payment Management'),
        ('paypervisit', 'Pay-per-visit'),
        ('product', 'Product Management'),
        ('pos', 'Point of Sale'),
        ('finance', 'Finance Management'),
        ('payroll', 'Payroll Management'),
        ('bill', 'Bill Management'),
        ('settings', 'Settings'),
        ('auth', 'Authentication'),
        ('system', 'System'),
    ]

    STATUS_CHOICES = [
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('partial', 'Partial Success'),
    ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='action_logs',
                            verbose_name=_("User"))
    action_type = models.CharField(_("Action Type"), max_length=50, choices=ACTION_TYPES)
    module = models.CharField(_("Module"), max_length=20, choices=MODULE_CHOICES)
    action_time = models.DateTimeField(_("Action Time"), auto_now_add=True)
    status = models.CharField(_("Status"), max_length=10, choices=STATUS_CHOICES, default='success')

    # Target information
    target_model = models.CharField(_("Target Model"), max_length=50, blank=True)
    target_id = models.CharField(_("Target ID"), max_length=50, blank=True)
    target_description = models.TextField(_("Target Description"), blank=True)

    # Change tracking
    before_values = models.JSONField(_("Before Values"), null=True, blank=True)
    after_values = models.JSONField(_("After Values"), null=True, blank=True)

    # Request information
    ip_address = models.GenericIPAddressField(_("IP Address"), null=True, blank=True)
    user_agent = models.TextField(_("User Agent"), blank=True)

    # Additional information
    description = models.TextField(_("Description"), blank=True)
    additional_data = models.JSONField(_("Additional Data"), null=True, blank=True)
    financial_impact = models.DecimalField(_("Financial Impact"), max_digits=15, decimal_places=2,
                                         null=True, blank=True, help_text="Amount in KHR")

    class Meta:
        verbose_name = _("User Action Log")
        verbose_name_plural = _("User Action Logs")
        ordering = ['-action_time']
        indexes = [
            models.Index(fields=['user', 'action_time']),
            models.Index(fields=['action_type', 'action_time']),
            models.Index(fields=['module', 'action_time']),
            models.Index(fields=['ip_address', 'action_time']),
        ]

    def __str__(self):
        user_name = self.user.username if self.user else "Unknown"
        return f"{user_name} - {self.get_action_type_display()} - {self.action_time.strftime('%Y-%m-%d %H:%M:%S')}"

    @classmethod
    def log_action(cls, user, action_type, module, status='success', target_model='', target_id='',
                   target_description='', before_values=None, after_values=None, ip_address=None,
                   user_agent='', description='', additional_data=None, financial_impact=None):
        """
        Create a log entry for a user action
        """
        return cls.objects.create(
            user=user,
            action_type=action_type,
            module=module,
            status=status,
            target_model=target_model,
            target_id=target_id,
            target_description=target_description,
            before_values=before_values,
            after_values=after_values,
            ip_address=ip_address,
            user_agent=user_agent,
            description=description,
            additional_data=additional_data,
            financial_impact=financial_impact
        )


# User role choices are defined above

# Models removed as per requirements

