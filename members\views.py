from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.utils import timezone
from django.db import connection
from django.contrib.auth.decorators import login_required
from .models import Member, Package
from datetime import datetime, timedelta
from core.decorators import module_permission_required
from core.logging_utils import log_create_action, log_edit_action, log_delete_action

@login_required
@module_permission_required(module='member', required_level='view')
def index(request):
    """
    Display list of members
    """
    packages = Package.objects.all()

    # Get all members
    members = Member.objects.all()

    # Get the current date
    current_date = timezone.now().date()

    # Create a list to store members with corrected due amounts
    corrected_members = []

    # Process each member to correct their due amount if they have an active package
    for member in members:
        # Check if the member has an active package that covers the current month
        if member.end_date >= current_date:
            # Check if the member has made any payments
            from payment.models import Payment
            payments = Payment.objects.filter(member=member)
            has_made_payments = payments.exists()

            # Create a copy of the member object
            from copy import copy
            display_member = copy(member)

            if has_made_payments:
                # Calculate the total amount paid by the member
                total_paid = sum(payment.amount_khr for payment in payments)

                # Calculate the remaining amount due
                package_price = member.package.price_khr if member.package else 0
                remaining_due = max(0, package_price - total_paid)

                if total_paid >= package_price:
                    # Member has paid the full package price
                    display_member.due_payment = 0
                    display_member.payment_status = 'paid'

                    # Update the actual member's payment status if needed
                    if member.payment_status != 'paid':
                        member.payment_status = 'paid'
                        member.save()
                else:
                    # Member has made a partial payment
                    display_member.due_payment = remaining_due
                    display_member.payment_status = 'pending'

                    # Update the actual member's payment status if needed
                    if member.payment_status != 'pending':
                        member.payment_status = 'pending'
                        member.save()
            else:
                # Member has an active package but hasn't made any payments
                # Set the due payment to the package price
                display_member.due_payment = member.package.price_khr
                display_member.payment_status = 'pending'

                # Make sure the payment status is 'pending'
                if member.payment_status != 'pending':
                    member.payment_status = 'pending'
                    member.save()

            corrected_members.append(display_member)
        else:
            # Member doesn't have an active package or it has expired
            corrected_members.append(member)

    # Replace the original members list with the corrected one
    members_list = corrected_members

    # Pagination with configurable items per page
    from django.core.paginator import Paginator

    # Get items per page from request or use default (10)
    items_per_page = request.GET.get('items_per_page', 10)
    try:
        items_per_page = int(items_per_page)
        # Validate items per page (between 5 and 100)
        if items_per_page < 5:
            items_per_page = 5
        elif items_per_page > 100:
            items_per_page = 100
    except (ValueError, TypeError):
        items_per_page = 10

    paginator = Paginator(members_list, items_per_page)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get paginated members
    members = page_obj

    context = {
        'page_obj': page_obj,
        'packages': packages,
        'members': members,  # For template compatibility
        'items_per_page': items_per_page,
    }
    return render(request, 'member/index.html', context)

@login_required
@module_permission_required(module='member', required_level='edit')
def create_member(request):
    """
    Create a new member
    """
    packages = Package.objects.all()

    if request.method == "POST":
        try:
            # Get form data
            name = request.POST.get("name")
            gender = request.POST.get("gender")
            dob = request.POST.get("dob") or None  # Handle empty date
            contact = request.POST.get("contact")
            telegram = request.POST.get("telegram", "")
            address = request.POST.get("address", "")
            package_id = request.POST.get("package")
            discount = request.POST.get("discount", 0)

            # Convert numeric values
            try:
                discount = int(discount)
            except (ValueError, TypeError):
                discount = 0

            # Get the selected package
            package = Package.objects.get(id=package_id)

            # Calculate start and end dates
            start_date = request.POST.get("start_date")
            if start_date:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            else:
                start_date = timezone.now().date()

            end_date = start_date + timedelta(days=package.duration * 30)  # Approximate month length

            # Generate member ID
            last_member = Member.objects.order_by('-id').first()
            if last_member:
                last_id = int(last_member.member_id.split('-')[1])
                new_id = last_id + 1
            else:
                new_id = 1001

            member_id = f"M-{new_id}"

            # Calculate due amount (package price minus discount)
            due_amount = max(0, package.price_khr - discount)

            # Create the member
            member = Member.objects.create(
                member_id=member_id,
                name=name,
                gender=gender,
                dob=dob,
                contact=contact,
                telegram=telegram,
                address=address,
                package=package,
                start_date=start_date,
                end_date=end_date,
                discount=discount,
                due_payment=due_amount,
                payment_status='pending',  # Default to pending for new registrations until they make a payment
            )

            # Handle photo upload if provided
            if 'photo' in request.FILES:
                member.photo = request.FILES['photo']
                member.save()

            # Log the member creation
            log_create_action(
                request=request,
                module='member',
                target_model='Member',
                target_id=member_id,
                target_description=f'Member {name} (ID: {member_id})',
                additional_data={
                    'package': package.name,
                    'package_duration': package.duration,
                    'package_price': package.price_khr,
                    'discount': discount,
                    'due_amount': due_amount,
                    'start_date': str(start_date),
                    'end_date': str(end_date),
                    'contact': contact
                }
            )

            messages.success(request, f"Member {name} registered successfully with ID {member_id}")
            return redirect('member:index')

        except Exception as e:
            messages.error(request, f"Error registering member: {str(e)}")

    context = {
        'packages': packages,
    }
    return render(request, 'member/member_form.html', context)

@login_required
@module_permission_required(module='member', required_level='edit')
def edit(request, pk):
    """
    Edit member details
    """
    member = get_object_or_404(Member, pk=pk)
    packages = Package.objects.all()

    if request.method == "POST":
        try:
            # Get form data
            member.name = request.POST.get("name")
            member.gender = request.POST.get("gender")
            member.dob = request.POST.get("dob") or None  # Handle empty date
            member.contact = request.POST.get("contact")
            member.telegram = request.POST.get("telegram", "")
            member.address = request.POST.get("address", "")

            # Get discount and due payment
            discount = request.POST.get("discount", 0)
            due_payment = request.POST.get("due_payment", 0)

            # Convert numeric values
            try:
                member.discount = int(discount)
            except (ValueError, TypeError):
                member.discount = 0

            try:
                member.due_payment = int(due_payment)
            except (ValueError, TypeError):
                member.due_payment = 0

            # Update package if changed
            package_id = request.POST.get("package")
            if package_id and int(package_id) != member.package.id:
                package = Package.objects.get(id=package_id)
                member.package = package

                # Recalculate end date based on new package
                member.end_date = member.start_date + timedelta(days=package.duration * 30)

            # Handle photo upload if provided
            if 'photo' in request.FILES:
                member.photo = request.FILES['photo']

            member.save()

            # Log the member edit
            log_edit_action(
                request=request,
                module='member',
                target_model='Member',
                target_id=member.member_id,
                target_description=f'Member {member.name} (ID: {member.member_id})',
                additional_data={
                    'package': member.package.name if member.package else None,
                    'discount': member.discount,
                    'due_payment': member.due_payment,
                    'contact': member.contact,
                    'photo_updated': 'photo' in request.FILES
                }
            )

            messages.success(request, f"Member {member.name} updated successfully")
            return redirect('member:index')

        except Exception as e:
            messages.error(request, f"Error updating member: {str(e)}")

    # Get current month and year for display
    current_date = datetime.now()
    current_month = current_date.strftime('%B')
    current_year = current_date.year

    # Get actual payment history from the database
    from payment.models import Payment

    # Get all payments for this member, ordered by date (newest first)
    payments = Payment.objects.filter(member=member).order_by('-payment_date')

    # Initialize bills list
    bills = []

    # Get package price for calculations
    package_price = member.package.price_khr if member.package else 0

    # Group payments by month for better organization
    from collections import defaultdict
    payments_by_month = defaultdict(list)

    # Track total payments for the current month
    current_month_key = f"{current_year}-{current_month}"
    current_month_total = 0

    # Process all payments and group them by month
    for payment in payments:
        payment_month = payment.payment_date.strftime('%B')
        payment_year = payment.payment_date.year
        month_key = f"{payment_year}-{payment_month}"

        # Add to the appropriate month group
        payments_by_month[month_key].append(payment)

        # Track current month total
        if month_key == current_month_key:
            current_month_total += payment.amount_khr

    # Check if the member has an active package that covers the current month
    # If they do, we shouldn't show a due amount for the current month
    has_active_package = member.end_date >= current_date.date()

    # Check if the member has made any payments
    has_made_payments = payments.exists()

    # If the member has an active package, we should temporarily set their due payment to 0
    # for display purposes, since they shouldn't owe anything for months covered by their package
    # (This is handled in the display_member object below)

    # Add current month's status (even if no payment was made)
    # This shows the overall status for the current month
    if has_active_package:
        if has_made_payments:
            # Calculate the total amount paid by the member
            total_paid = sum(payment.amount_khr for payment in payments)

            # Calculate the remaining amount due
            remaining_due = max(0, package_price - total_paid)

            if total_paid >= package_price:
                # Member has paid the full package price
                bills.append({
                    'month': current_month,
                    'year': current_year,
                    'reason': 'Membership Fee',
                    'total_amount': package_price,
                    'payed_amount': package_price,  # They've already paid for this month through their package
                    'due_amount': 0,  # They don't owe anything for this month
                    'payment_date': current_date.strftime('%Y-%m-%d'),
                    'payment_method': 'Package',
                    'status': 'Covered by Package',
                    'is_summary': True  # Flag to style differently in template
                })
            else:
                # Member has made a partial payment
                bills.append({
                    'month': current_month,
                    'year': current_year,
                    'reason': 'Membership Fee',
                    'total_amount': package_price,
                    'payed_amount': total_paid,  # They've paid this much so far
                    'due_amount': remaining_due,  # They still owe this much
                    'payment_date': current_date.strftime('%Y-%m-%d'),
                    'payment_method': 'Various',
                    'status': 'Partial',
                    'is_summary': True  # Flag to style differently in template
                })
        else:
            # Member has an active package but hasn't made any payments yet
            bills.append({
                'month': current_month,
                'year': current_year,
                'reason': 'Membership Fee',
                'total_amount': package_price,
                'payed_amount': 0,  # They haven't paid yet
                'due_amount': package_price,  # They owe the full package price
                'payment_date': current_date.strftime('%Y-%m-%d'),
                'payment_method': 'N/A',
                'status': 'Payment Required',
                'is_summary': True  # Flag to style differently in template
            })
    else:
        # Member doesn't have an active package or their package has expired
        # Show the regular monthly fee
        bills.append({
            'month': current_month,
            'year': current_year,
            'reason': 'Membership Fee',
            'total_amount': package_price,
            'payed_amount': min(current_month_total, package_price),
            'due_amount': max(0, package_price - current_month_total),
            'payment_date': current_date.strftime('%Y-%m-%d'),
            'payment_method': 'Various',
            'status': 'Completed' if member.due_payment == 0 else 'Partial',
            'is_summary': True  # Flag to style differently in template
        })

    # Add individual payments for the current month
    current_month_payments = payments_by_month.get(current_month_key, [])

    # Track running total to determine if payments are regular or additional
    running_total = 0

    for payment in current_month_payments:
        # Determine if this is a regular payment or an additional payment
        running_total += payment.amount_khr
        is_additional = running_total > package_price
        remaining_for_package = max(0, package_price - (running_total - payment.amount_khr))

        # Amount that goes toward package vs. extension
        package_portion = min(payment.amount_khr, remaining_for_package)
        extension_portion = max(0, payment.amount_khr - package_portion)

        # Determine reason based on payment notes and amount
        if "Additional payment" in (payment.notes or ""):
            reason = "Membership Extension"
        elif is_additional:
            reason = "Membership Extension"
        else:
            reason = "Membership Fee"

        # Add the payment to the bills list
        bills.append({
            'month': payment.payment_date.strftime('%B'),
            'year': payment.payment_date.year,
            'reason': reason,
            'total_amount': package_price if not is_additional else extension_portion,
            'payed_amount': payment.amount_khr,
            'due_amount': max(0, package_price - running_total) if not is_additional else 0,
            'payment_date': payment.payment_date.strftime('%Y-%m-%d'),
            'payment_method': payment.get_payment_method_display(),
            'status': 'Extension' if is_additional else ('Completed' if running_total >= package_price else 'Partial'),
            'invoice_no': payment.invoice_no,
            'is_summary': False
        })

    # Add payments from previous months
    for month_key, month_payments in payments_by_month.items():
        if month_key == current_month_key:
            continue  # Skip current month as we've already processed it

        # Calculate total for this month
        month_total = sum(p.amount_khr for p in month_payments)

        # Extract month and year from the key
        payment_year, payment_month = month_key.split('-')

        # Add a summary entry for this month
        bills.append({
            'month': payment_month,
            'year': payment_year,
            'reason': 'Membership Fee',
            'total_amount': package_price,
            'payed_amount': min(month_total, package_price),
            'due_amount': max(0, package_price - month_total),
            'payment_date': month_payments[0].payment_date.strftime('%Y-%m-%d'),
            'payment_method': 'Various',
            'status': 'Completed' if month_total >= package_price else 'Partial',
            'is_summary': True
        })

        # Add individual payments for this month
        running_total = 0
        for payment in month_payments:
            # Determine if this is a regular payment or an additional payment
            running_total += payment.amount_khr
            is_additional = running_total > package_price
            remaining_for_package = max(0, package_price - (running_total - payment.amount_khr))

            # Amount that goes toward package vs. extension
            package_portion = min(payment.amount_khr, remaining_for_package)
            extension_portion = max(0, payment.amount_khr - package_portion)

            # Determine reason based on payment notes and amount
            if "Additional payment" in (payment.notes or ""):
                reason = "Membership Extension"
            elif is_additional:
                reason = "Membership Extension"
            else:
                reason = "Membership Fee"

            bills.append({
                'month': payment.payment_date.strftime('%B'),
                'year': payment.payment_date.year,
                'reason': reason,
                'total_amount': package_price if not is_additional else extension_portion,
                'payed_amount': payment.amount_khr,
                'due_amount': max(0, package_price - running_total) if not is_additional else 0,
                'payment_date': payment.payment_date.strftime('%Y-%m-%d'),
                'payment_method': payment.get_payment_method_display(),
                'status': 'Extension' if is_additional else ('Completed' if running_total >= package_price else 'Partial'),
                'invoice_no': payment.invoice_no,
                'is_summary': False
            })

    # Create a temporary copy of the member object with the corrected due amount
    # This is just for display purposes and doesn't affect the database
    display_member = member

    # Create a copy of the member object
    from copy import copy
    display_member = copy(member)

    # Calculate the total amount paid by the member
    total_paid = sum(payment.amount_khr for payment in payments)

    # Calculate the remaining amount due
    remaining_due = max(0, package_price - total_paid)

    if has_active_package:
        if has_made_payments:
            # Member has an active package and has made payments
            if total_paid >= package_price:
                # Member has paid the full package price
                display_member.due_payment = 0
                display_member.payment_status = 'paid'

                # Update the actual member's payment status if needed
                if member.payment_status != 'paid':
                    member.payment_status = 'paid'
                    member.save()
            else:
                # Member has made a partial payment
                display_member.due_payment = remaining_due
                display_member.payment_status = 'pending'

                # Update the actual member's payment status if needed
                if member.payment_status != 'pending':
                    member.payment_status = 'pending'
                    member.save()
        else:
            # Member has an active package but hasn't made any payments
            # Set the due payment to the package price
            display_member.due_payment = package_price
            display_member.payment_status = 'pending'

            # Make sure the payment status is 'pending'
            if member.payment_status != 'pending':
                member.payment_status = 'pending'
                member.save()

    context = {
        'member': display_member,  # Use the display member with corrected due amount
        'packages': packages,
        'bills': bills,
        'month': current_month,
        'now': datetime.now()  # Pass the current date for template comparisons
    }
    return render(request, 'member/edit.html', context)

@login_required
@module_permission_required(module='member', required_level='view')
def print_card(request, pk):
    """
    Print member ID card
    """
    member = get_object_or_404(Member, pk=pk)
    context = {
        'member': member,
        'now': datetime.now(),  # Pass current date for the coach card
    }
    return render(request, 'member/print_card.html', context)

@login_required
@module_permission_required(module='member', required_level='edit')
def deactivate(request, pk):
    """
    Deactivate a member
    """
    member = get_object_or_404(Member, pk=pk)
    member.status = False
    member.save()
    messages.success(request, f"Member {member.name} deactivated successfully")
    return redirect('member:index')

@login_required
@module_permission_required(module='member', required_level='edit')
def activate(request, pk):
    """
    Activate a member
    """
    member = get_object_or_404(Member, pk=pk)
    member.status = True
    member.save()
    messages.success(request, f"Member {member.name} activated successfully")
    return redirect('member:index')



@login_required
@module_permission_required(module='member', required_level='full')
def delete(request, pk):
    """
    Delete a member and handle related records
    """
    member = get_object_or_404(Member, pk=pk)
    name = member.name
    member_id = member.member_id

    try:
        # Log the deletion before actually deleting
        log_delete_action(
            request=request,
            module='member',
            target_model='Member',
            target_id=member_id,
            target_description=f'Member {name} (ID: {member_id})',
            additional_data={
                'package': member.package.name if member.package else None,
                'contact': member.contact,
                'due_payment': member.due_payment,
                'payment_status': member.payment_status,
                'start_date': str(member.start_date),
                'end_date': str(member.end_date)
            }
        )

        # Try to delete the member directly
        member.delete()
        messages.success(request, f"Member {name} deleted successfully")
    except Exception as e:
        # If there's an error (likely due to foreign key constraints)
        try:
            # Try to manually delete related records in transaction tables
            with connection.cursor() as cursor:
                # First check if there are related records in transaction_transaction
                cursor.execute(
                    "SELECT id FROM transaction_transaction WHERE member_id = %s",
                    [pk]
                )
                transaction_ids = [row[0] for row in cursor.fetchall()]

                # Delete related records in transaction_payrollrecord
                if transaction_ids:
                    placeholders = ','.join(['%s'] * len(transaction_ids))
                    cursor.execute(
                        f"DELETE FROM transaction_payrollrecord WHERE transaction_id IN ({placeholders})",
                        transaction_ids
                    )

                # Then delete records from transaction_transaction
                cursor.execute(
                    "DELETE FROM transaction_transaction WHERE member_id = %s",
                    [pk]
                )

            # Now try to delete the member again
            member.delete()
            messages.success(request, f"Member {name} deleted successfully")
        except Exception as e:
            # If still failing, show an error message
            messages.error(request, f"Error deleting member: {str(e)}")

    return redirect('member:index')

# Data Cleaning View
@login_required
@module_permission_required(module='member', required_level='full')
def clean_data(request):
    """
    Clean data in Member and Payment tables
    """
    if request.method == "POST":
        action = request.POST.get("action")

        if action == "clean_transaction_table":
            # Clean the transaction_transaction table
            try:
                with connection.cursor() as cursor:
                    # First delete records from transaction_payrollrecord
                    cursor.execute("DELETE FROM transaction_payrollrecord")

                    # Then delete records from transaction_transaction
                    cursor.execute("DELETE FROM transaction_transaction")

                    messages.success(request, "Transaction tables cleaned successfully")
            except Exception as e:
                messages.error(request, f"Error cleaning transaction tables: {str(e)}")

        elif action == "clean_members":
            # Delete all members
            try:
                # First try to clean transaction tables
                with connection.cursor() as cursor:
                    # First delete records from transaction_payrollrecord
                    cursor.execute("DELETE FROM transaction_payrollrecord")
                    # Then delete records from transaction_transaction
                    cursor.execute("DELETE FROM transaction_transaction")

                # Then delete all members
                count = Member.objects.all().count()
                Member.objects.all().delete()
                messages.success(request, f"All {count} members deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting members: {str(e)}")

        elif action == "clean_payments":
            # Delete all payments
            try:
                from payment.models import Payment
                count = Payment.objects.all().count()
                Payment.objects.all().delete()
                messages.success(request, f"All {count} payments deleted successfully")
            except Exception as e:
                messages.error(request, f"Error deleting payments: {str(e)}")

        elif action == "clean_all":
            # Clean all data
            try:
                # First try to clean transaction tables
                with connection.cursor() as cursor:
                    # First delete records from transaction_payrollrecord
                    cursor.execute("DELETE FROM transaction_payrollrecord")
                    # Then delete records from transaction_transaction
                    cursor.execute("DELETE FROM transaction_transaction")

                # Delete all payments
                from payment.models import Payment
                payment_count = Payment.objects.all().count()
                Payment.objects.all().delete()

                # Delete all members
                member_count = Member.objects.all().count()
                Member.objects.all().delete()

                messages.success(request, f"All data cleaned successfully: {member_count} members and {payment_count} payments deleted")
            except Exception as e:
                messages.error(request, f"Error cleaning data: {str(e)}")

    # Get counts for display
    member_count = Member.objects.all().count()

    try:
        from payment.models import Payment
        payment_count = Payment.objects.all().count()
    except:
        payment_count = 0

    context = {
        'member_count': member_count,
        'payment_count': payment_count,
    }

    return render(request, 'member/clean_data.html', context)

# Package Management Views
@login_required
@module_permission_required(module='member', required_level='view')
def package_list(request):
    """
    Display list of packages
    """
    packages = Package.objects.all()
    context = {
        'packages': packages,
    }
    return render(request, 'member/package_list.html', context)

@login_required
@module_permission_required(module='member', required_level='edit')
def package_create(request):
    """
    Create a new package
    """
    if request.method == "POST":
        try:
            package_id = request.POST.get("package_id")
            name = request.POST.get("name")
            duration = int(request.POST.get("duration"))
            price_khr = int(request.POST.get("price_khr"))
            price_usd = request.POST.get("price_usd")
            access_type = request.POST.get("access_type")
            description = request.POST.get("description")

            # Auto-generate package ID if not provided or set to 'auto'
            if not package_id or package_id == 'auto':
                last_package = Package.objects.order_by('-id').first()
                if last_package and last_package.package_id.startswith('PKG-'):
                    try:
                        last_id = int(last_package.package_id.split('-')[1])
                        new_id = last_id + 1
                    except (ValueError, IndexError):
                        new_id = 1
                else:
                    new_id = 1
                package_id = f"PKG-{new_id:03d}"

            # Create the package
            package = Package.objects.create(
                package_id=package_id,
                name=name,
                duration=duration,
                price_khr=price_khr,
                price_usd=float(price_usd) if price_usd else None,
                access_type=access_type,
                description=description
            )

            # Log the package creation
            log_create_action(
                request=request,
                module='member',
                target_model='Package',
                target_id=package_id,
                target_description=f'Package {name} (ID: {package_id})',
                additional_data={
                    'duration': duration,
                    'price_khr': price_khr,
                    'price_usd': float(price_usd) if price_usd else None,
                    'access_type': access_type,
                    'description': description
                }
            )

            messages.success(request, f"Package {name} created successfully")
            return redirect('member:package_list')

        except Exception as e:
            messages.error(request, f"Error creating package: {str(e)}")

    context = {
        'access_types': [
            ('all_hours', 'All Hours'),
            ('peak_only', 'Peak Hours Only')
        ]
    }
    return render(request, 'member/package_form.html', context)

@login_required
@module_permission_required(module='member', required_level='edit')
def package_edit(request, pk):
    """
    Edit a package
    """
    package = get_object_or_404(Package, pk=pk)

    if request.method == "POST":
        try:
            package.name = request.POST.get("name")
            package.duration = int(request.POST.get("duration"))
            package.price_khr = int(request.POST.get("price_khr"))

            price_usd = request.POST.get("price_usd")
            package.price_usd = float(price_usd) if price_usd else None

            package.access_type = request.POST.get("access_type")
            package.description = request.POST.get("description")

            package.save()

            # Log the package edit
            log_edit_action(
                request=request,
                module='member',
                target_model='Package',
                target_id=package.package_id,
                target_description=f'Package {package.name} (ID: {package.package_id})',
                additional_data={
                    'duration': package.duration,
                    'price_khr': package.price_khr,
                    'price_usd': package.price_usd,
                    'access_type': package.access_type,
                    'description': package.description
                }
            )

            messages.success(request, f"Package {package.name} updated successfully")
            return redirect('member:package_list')

        except Exception as e:
            messages.error(request, f"Error updating package: {str(e)}")

    context = {
        'package': package,
        'access_types': [
            ('all_hours', 'All Hours'),
            ('peak_only', 'Peak Hours Only')
        ]
    }
    return render(request, 'member/package_form.html', context)

@login_required
@module_permission_required(module='member', required_level='full')
def package_delete(request, pk):
    """
    Delete a package
    """
    package = get_object_or_404(Package, pk=pk)

    # Check if the package is being used by any members
    if package.members.exists():
        messages.error(request, f"Cannot delete package {package.name} as it is being used by members")
        return redirect('member:package_list')

    # Log the package deletion before deleting
    log_delete_action(
        request=request,
        module='member',
        target_model='Package',
        target_id=package.package_id,
        target_description=f'Package {package.name} (ID: {package.package_id})',
        additional_data={
            'duration': package.duration,
            'price_khr': package.price_khr,
            'price_usd': package.price_usd,
            'access_type': package.access_type,
            'description': package.description
        }
    )

    package.delete()
    messages.success(request, f"Package {package.name} deleted successfully")
    return redirect('member:package_list')



@login_required
@module_permission_required(module='member', required_level='view')
def bill(request):
    """
    Display members with due payments
    """
    # Get the current date
    current_date = timezone.now().date()

    # Get all members
    members = Member.objects.filter(status=True)

    # Create a list to store members with corrected due amounts
    corrected_members = []

    # Process each member to correct their due amount if they have an active package
    for member in members:
        # Check if the member has an active package that covers the current month
        if member.end_date >= current_date:
            # Check if the member has made any payments
            from payment.models import Payment
            payments = Payment.objects.filter(member=member)
            has_made_payments = payments.exists()

            # Create a copy of the member object
            from copy import copy
            display_member = copy(member)

            if has_made_payments:
                # Calculate the total amount paid by the member
                total_paid = sum(payment.amount_khr for payment in payments)

                # Calculate the remaining amount due
                package_price = member.package.price_khr if member.package else 0
                remaining_due = max(0, package_price - total_paid)

                if total_paid >= package_price:
                    # Member has paid the full package price
                    display_member.due_payment = 0
                    display_member.payment_status = 'paid'

                    # Update the actual member's payment status if needed
                    if member.payment_status != 'paid':
                        member.payment_status = 'paid'
                        member.save()
                else:
                    # Member has made a partial payment
                    display_member.due_payment = remaining_due
                    display_member.payment_status = 'pending'

                    # Update the actual member's payment status if needed
                    if member.payment_status != 'pending':
                        member.payment_status = 'pending'
                        member.save()
            else:
                # Member has an active package but hasn't made any payments
                # Set the due payment to the package price
                display_member.due_payment = member.package.price_khr
                display_member.payment_status = 'pending'

                # Make sure the payment status is 'pending'
                if member.payment_status != 'pending':
                    member.payment_status = 'pending'
                    member.save()

            corrected_members.append(display_member)
        else:
            # Member doesn't have an active package or it has expired
            corrected_members.append(member)

    # Replace the original members list with the corrected one
    members = corrected_members

    # Filter members with due payments
    members_with_due = [member for member in members if member.due_payment > 0]

    # Handle search
    if request.method == "POST":
        search_term = request.POST.get("search", "").strip()
        if search_term:
            members_with_due = [m for m in members_with_due if
                               search_term.lower() in m.name.lower() or
                               search_term.lower() in m.member_id.lower()]

    context = {
        'members': members_with_due
    }
    return render(request, 'member/bill.html', context)
