{% extends "../base.html" %}
{% load custom_filters %}
{% load permission_tags %}

{% block css %}
<style>
    /* Responsive table improvements */
    @media (max-width: 768px) {
        .purchase-table th:nth-child(2),
        .purchase-table td:nth-child(2) {
            display: none; /* Hide Supplier column on mobile */
        }

        .purchase-table th:nth-child(5),
        .purchase-table td:nth-child(5) {
            display: none; /* Hide Created By column on mobile */
        }

        .delete-purchase-btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.625rem !important;
        }

        .delete-purchase-btn i {
            margin-right: 0 !important;
        }

        .delete-purchase-btn .btn-text {
            display: none;
        }
    }

    @media (max-width: 640px) {
        .purchase-table th:nth-child(3),
        .purchase-table td:nth-child(3) {
            display: none; /* Hide Date column on small mobile */
        }
    }
</style>
{% endblock %}



{% block body %}
<!-- Hidden CSRF token for JavaScript -->
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Add Button -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Purchases</h3>
            <a href="{% url 'product:create_purchase' %}" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-200">
                <i class="fas fa-plus mr-2"></i> Record New Purchase
            </a>
        </div>

        <!-- Recent Purchases List -->
        <div class="bg-white p-4 rounded shadow-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold">Purchase History</h3>

                <!-- Date Filter Form -->
                <form method="get" class="flex items-center space-x-2">
                    <div class="flex items-center">
                        <label for="start_date" class="mr-2 text-sm font-medium">From:</label>
                        <input
                            type="date"
                            id="start_date"
                            name="start_date"
                            class="border rounded p-1 text-sm"
                            value="{{ start_date }}"
                            required
                        >
                    </div>
                    <div class="flex items-center">
                        <label for="end_date" class="mr-2 text-sm font-medium">To:</label>
                        <input
                            type="date"
                            id="end_date"
                            name="end_date"
                            class="border rounded p-1 text-sm"
                            value="{{ end_date }}"
                            required
                        >
                    </div>
                    <button type="submit" class="bg-blue-900 text-white px-3 py-1 rounded text-sm">Filter</button>
                    {% if filter_active %}
                    <a href="{% url 'product:purchases' %}" class="bg-gray-500 text-white px-3 py-1 rounded text-sm">Reset</a>
                    {% endif %}
                </form>
            </div>

            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left purchase-table">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-6 py-3">Transaction ID</th>
                            <th scope="col" class="px-6 py-3">Supplier</th>
                            <th scope="col" class="px-6 py-3">Date</th>
                            <th scope="col" class="px-6 py-3">Total Amount</th>
                            <th scope="col" class="px-6 py-3">Created By</th>
                            <th scope="col" class="px-6 py-3">Details</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for purchase in page_obj %}
                        <tr class="bg-white border">
                            <td class="px-6 py-4">{{ purchase.trxId }}</td>
                            <td class="px-6 py-4">{{ purchase.supplier.name|default:"None" }}</td>
                            <td class="px-6 py-4">{{ purchase.date }}</td>
                            <td class="px-6 py-4">{{ purchase.total_amount|format_khr }}</td>
                            <td class="px-6 py-4">{{ purchase.created_by.username }}</td>
                            <td class="px-6 py-4">
                                <button class="text-blue-600 hover:underline view-details" data-purchase-id="{{ purchase.id }}">View Details</button>
                            </td>
                            <td class="px-6 py-4">
                                {% has_permission user 'purchase' 'full' as can_delete_purchase %}
                                {% if can_delete_purchase %}
                                <button
                                    type="button"
                                    class="bg-red-600 hover:bg-red-700 text-white font-medium py-1 px-3 rounded text-xs transition duration-200 delete-purchase-btn"
                                    data-purchase-id="{{ purchase.id }}"
                                    data-purchase-trx="{{ purchase.trxId }}"
                                    data-purchase-amount="{{ purchase.total_amount|format_khr }}"
                                    data-purchase-created-by="{{ purchase.created_by.username|default:'Unknown' }}"
                                    title="Delete Purchase">
                                    <i class="fas fa-trash mr-1"></i><span class="btn-text">Delete</span>
                                </button>
                                {% else %}
                                <span class="text-gray-400 text-xs">No Access</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr class="bg-gray-50 hidden purchase-details" id="details-{{ purchase.id }}">
                            <td colspan="7" class="px-6 py-4">
                                <div class="bg-white rounded-lg p-4 shadow-sm border">
                                    <!-- Purchase Summary -->
                                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4 pb-4 border-b">
                                        <div>
                                            <h5 class="font-semibold text-gray-700 mb-1">Transaction ID</h5>
                                            <p class="text-sm text-gray-600">{{ purchase.trxId }}</p>
                                        </div>
                                        <div>
                                            <h5 class="font-semibold text-gray-700 mb-1">Purchase Date</h5>
                                            <p class="text-sm text-gray-600">{{ purchase.date|date:"M d, Y H:i" }}</p>
                                        </div>
                                        <div>
                                            <h5 class="font-semibold text-gray-700 mb-1">Created By</h5>
                                            <p class="text-sm text-gray-600">{{ purchase.created_by.username }}</p>
                                        </div>
                                        {% if purchase.supplier %}
                                        <div>
                                            <h5 class="font-semibold text-gray-700 mb-1">Supplier</h5>
                                            <p class="text-sm text-gray-600">{{ purchase.supplier.name }}</p>
                                            {% if purchase.supplier.phone %}
                                            <p class="text-xs text-gray-500">{{ purchase.supplier.phone }}</p>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                        <div>
                                            <h5 class="font-semibold text-gray-700 mb-1">Total Amount</h5>
                                            <p class="text-sm font-semibold text-blue-600">{{ purchase.total_amount|format_khr }}</p>
                                        </div>
                                    </div>

                                    <!-- Purchase Items -->
                                    <div>
                                        <h4 class="font-semibold text-gray-800 mb-3">Purchase Items ({{ purchase.items.count }} item{{ purchase.items.count|pluralize }})</h4>
                                        <div class="overflow-x-auto">
                                            <table class="w-full text-sm border border-gray-200 rounded-lg">
                                                <thead class="bg-blue-50">
                                                    <tr>
                                                        <th class="px-3 py-2 text-left font-semibold text-gray-700 border-b">Product</th>
                                                        <th class="px-3 py-2 text-left font-semibold text-gray-700 border-b">SKU</th>
                                                        <th class="px-3 py-2 text-left font-semibold text-gray-700 border-b">Supplier</th>
                                                        <th class="px-3 py-2 text-left font-semibold text-gray-700 border-b">Quantity</th>
                                                        <th class="px-3 py-2 text-left font-semibold text-gray-700 border-b">Cost Price</th>
                                                        <th class="px-3 py-2 text-right font-semibold text-gray-700 border-b">Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for item in purchase.items.all %}
                                                    <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
                                                        <td class="px-3 py-2 border-b">
                                                            <div class="font-medium text-gray-900">{{ item.product.name }}</div>
                                                            {% if item.product.category %}
                                                            <div class="text-xs text-gray-500">{{ item.product.category.name }}</div>
                                                            {% endif %}
                                                        </td>
                                                        <td class="px-3 py-2 border-b text-gray-600">{{ item.product.sku }}</td>
                                                        <td class="px-3 py-2 border-b">
                                                            {% if item.supplier %}
                                                                <span class="text-gray-900">{{ item.supplier.name }}</span>
                                                            {% else %}
                                                                <span class="text-gray-500 italic">No Supplier</span>
                                                            {% endif %}
                                                        </td>
                                                        <td class="px-3 py-2 border-b">
                                                            <span class="text-gray-900">{{ item.display_quantity }}</span>
                                                        </td>
                                                        <td class="px-3 py-2 border-b">
                                                            <span class="text-gray-900">{{ item.display_cost }}</span>
                                                        </td>
                                                        <td class="px-3 py-2 border-b text-right font-medium text-gray-900">
                                                            {{ item.total_cost|format_khr }}
                                                        </td>
                                                    </tr>
                                                    {% empty %}
                                                    <tr>
                                                        <td colspan="6" class="px-3 py-4 text-center text-gray-500">No items found</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <!-- Notes Section -->
                                    {% if purchase.notes %}
                                    <div class="mt-4 pt-4 border-t">
                                        <h5 class="font-semibold text-gray-700 mb-2">Notes</h5>
                                        <p class="text-sm text-gray-600 bg-gray-50 p-3 rounded">{{ purchase.notes }}</p>
                                    </div>
                                    {% endif %}


                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="7" class="px-6 py-4 text-center">
                                {% if filter_active %}
                                No purchases found for the selected date range.
                                {% else %}
                                No purchases recorded yet.
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Enhanced Pagination -->
            <div class="bg-white border-t border-gray-200">
                <!-- Mobile Pagination (Simple Previous/Next) -->
                <div class="flex justify-between items-center px-4 py-3 sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-chevron-left mr-2"></i>Previous
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            <i class="fas fa-chevron-left mr-2"></i>Previous
                        </span>
                    {% endif %}

                    <span class="text-sm text-gray-700">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>

                    {% if page_obj.has_next %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next<i class="fas fa-chevron-right ml-2"></i>
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            Next<i class="fas fa-chevron-right ml-2"></i>
                        </span>
                    {% endif %}
                </div>

                <!-- Desktop Pagination (Full Controls) -->
                <div class="hidden sm:flex sm:flex-col lg:flex-row lg:items-center lg:justify-between px-6 py-4 space-y-4 lg:space-y-0">
                    <!-- Left side: Results info and items per page -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> entries
                            </p>
                        </div>
                        <div class="flex items-center">
                            <label for="items-per-page" class="text-sm text-gray-700 mr-2">Items per page:</label>
                            <select id="items-per-page" name="items_per_page" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500" onchange="changeItemsPerPage()">
                                <option value="5" {% if items_per_page == 5 %}selected{% endif %}>5</option>
                                <option value="10" {% if items_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="25" {% if items_per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if items_per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if items_per_page == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>
                    </div>

                    <!-- Right side: Pagination controls -->
                    {% if page_obj.has_other_pages %}
                    <div class="flex items-center space-x-2">
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <!-- First Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page=1"
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="First page">
                                    <span class="sr-only">First</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">First</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </span>
                            {% endif %}

                            <!-- Previous Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            {% endif %}

                            <!-- Page Numbers -->
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span aria-current="page" class="relative z-10 inline-flex items-center bg-blue-900 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-900">{{ num }}</span>
                                {% elif num > page_obj.number|add:"-3" and num < page_obj.number|add:"3" %}
                                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                       class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                {% elif num == 1 or num == page_obj.paginator.num_pages %}
                                    {% if num != page_obj.number %}
                                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                           class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                    {% endif %}
                                {% elif num == page_obj.number|add:"-4" or num == page_obj.number|add:"4" %}
                                    <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                                {% endif %}
                            {% endfor %}

                            <!-- Next Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            {% endif %}

                            <!-- Last Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.paginator.num_pages }}"
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="Last page">
                                    <span class="sr-only">Last</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Last</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </span>
                            {% endif %}
                        </nav>

                        <!-- Jump to Page -->
                        <div class="flex items-center ml-4">
                            <span class="text-sm text-gray-700 mr-2">Go to page:</span>
                            <input type="number" id="jump-to-page" min="1" max="{{ page_obj.paginator.num_pages }}" value="{{ page_obj.number }}"
                                   class="w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500">
                            <button id="jump-page-btn" class="ml-2 bg-blue-900 text-white px-2 py-1 rounded text-sm hover:bg-blue-800 transition duration-200">Go</button>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Items per page functionality
        const itemsPerPageSelect = document.getElementById('items-per-page');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', function() {
                changeItemsPerPage();
            });
        }

        // Jump to page functionality
        const jumpToPageInput = document.getElementById('jump-to-page');
        const jumpPageBtn = document.getElementById('jump-page-btn');

        if (jumpToPageInput && jumpPageBtn) {
            jumpPageBtn.addEventListener('click', function() {
                const pageNum = parseInt(jumpToPageInput.value);
                const maxPage = parseInt(jumpToPageInput.getAttribute('max'));

                if (pageNum && pageNum > 0 && pageNum <= maxPage) {
                    // Build the URL with the current query parameters
                    let url = new URL(window.location.href);
                    let params = new URLSearchParams(url.search);

                    // Update or add the page parameter
                    params.set('page', pageNum);

                    // Redirect to the new URL
                    window.location.href = `${url.pathname}?${params.toString()}`;
                } else {
                    alert(`Please enter a valid page number between 1 and ${maxPage}`);
                    jumpToPageInput.value = "{{ page_obj.number }}";
                }
            });

            // Allow Enter key to trigger jump
            jumpToPageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    jumpPageBtn.click();
                }
            });
        }

        // Toggle purchase details functionality
        document.querySelectorAll('.view-details').forEach(button => {
            button.addEventListener('click', function() {
                const purchaseId = this.getAttribute('data-purchase-id');
                const detailsRow = document.getElementById('details-' + purchaseId);

                if (detailsRow) {
                    detailsRow.classList.toggle('hidden');

                    // Update button text based on visibility
                    if (detailsRow.classList.contains('hidden')) {
                        this.textContent = 'View Details';
                    } else {
                        this.textContent = 'Hide Details';
                    }
                } else {
                    console.error('Details row not found for purchase ID:', purchaseId);
                }
            });
        });

        // Delete purchase functionality with confirmation
        document.querySelectorAll('.delete-purchase-btn').forEach(button => {
            button.addEventListener('click', function() {
                const purchaseId = this.getAttribute('data-purchase-id');
                const purchaseTrx = this.getAttribute('data-purchase-trx');
                const purchaseAmount = this.getAttribute('data-purchase-amount');
                const createdBy = this.getAttribute('data-purchase-created-by');

                // Create confirmation dialog
                const confirmMessage = `Are you sure you want to delete this purchase?\n\n` +
                    `Transaction ID: ${purchaseTrx}\n` +
                    `Amount: ${purchaseAmount}\n` +
                    `Created by: ${createdBy}\n\n` +
                    `This action cannot be undone. The purchase amount will be added back to gym funds.`;

                if (confirm(confirmMessage)) {
                    // Create a form and submit it to delete the purchase
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/product/purchases/delete/${purchaseId}/`;

                    // Add CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Submit the form
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Function to change items per page
    function changeItemsPerPage() {
        const itemsPerPage = document.getElementById('items-per-page').value;

        // Build the URL with current query parameters
        let url = new URL(window.location.href);
        let params = new URLSearchParams(url.search);

        // Update items per page and reset to page 1
        params.set('items_per_page', itemsPerPage);
        params.set('page', '1');

        // Redirect to the new URL
        window.location.href = `${url.pathname}?${params.toString()}`;
    }
</script>
{% endblock %}
