from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Sum
from django.utils import timezone
from datetime import datetime, date
from django.http import JsonResponse
from user.models import User, MetaData
from .models import SalaryPayment, SlipTemplate
from core.utils import generate_unique_payroll_id
from core.decorators import module_permission_required
from core.logging_utils import log_create_action, log_edit_action, log_delete_action

@login_required
@module_permission_required(module='payroll', required_level='view')
def index(request):
    """
    Display list of salary payments
    """
    # Get all employees
    employees = User.objects.filter(is_employee=True, status=True).exclude(role='admin')

    # Get filter parameters
    employee_filter = request.GET.get('employee', '')
    month_filter = request.GET.get('month', '')
    status_filter = request.GET.get('status', '')

    # Start with all payments
    payments = SalaryPayment.objects.all().order_by('-month')

    # Apply filters if provided
    if employee_filter:
        payments = payments.filter(employee__id=employee_filter)

    if month_filter:
        try:
            month_date = datetime.strptime(month_filter, '%Y-%m')
            payments = payments.filter(month__year=month_date.year, month__month=month_date.month)
        except ValueError:
            pass

    if status_filter:
        payments = payments.filter(payment_status=status_filter)

    # Calculate total paid amount
    total_paid = payments.filter(payment_status='paid').aggregate(total=Sum('final_pay'))['total'] or 0

    # Pagination with configurable items per page
    from django.core.paginator import Paginator

    # Get items per page from request or use default (10)
    items_per_page = request.GET.get('items_per_page', 10)
    try:
        items_per_page = int(items_per_page)
        # Validate items per page (between 5 and 100)
        if items_per_page < 5:
            items_per_page = 5
        elif items_per_page > 100:
            items_per_page = 100
    except (ValueError, TypeError):
        items_per_page = 10

    paginator = Paginator(payments, items_per_page)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get paginated payments
    payments = page_obj

    context = {
        'page_obj': page_obj,
        'employees': employees,
        'payments': payments,  # For template compatibility
        'total_paid': total_paid,
        'employee_filter': employee_filter,
        'month_filter': month_filter,
        'status_filter': status_filter,
        'current_month': date.today().strftime('%Y-%m'),
        'items_per_page': items_per_page,
    }
    return render(request, 'payroll/index.html', context)

@login_required
@module_permission_required(module='payroll', required_level='edit')
def create_salary(request):
    """
    Create a new salary payment
    """
    # Get all employees
    employees = User.objects.filter(is_employee=True, status=True).exclude(role='admin')

    if request.method == "POST":
        try:
            employee_id = request.POST.get("employee")
            month_year = request.POST.get("month")
            base_salary = int(request.POST.get("base_salary"))
            bonus = int(request.POST.get("bonus") or 0)
            deduction = int(request.POST.get("deduction") or 0)
            overtime_hours = int(request.POST.get("overtime_hours") or 0)
            payment_method = request.POST.get("payment_method")
            employment_type = request.POST.get("employment_type")
            notes = request.POST.get("notes")

            # Get the selected employee
            employee = User.objects.get(id=employee_id)

            # Parse month and year
            month_date = datetime.strptime(month_year, '%Y-%m')

            # Check if a payment record already exists for this employee and month
            existing_payment = SalaryPayment.objects.filter(
                employee=employee,
                month__year=month_date.year,
                month__month=month_date.month
            ).first()

            if existing_payment:
                messages.error(request, f"A salary payment record already exists for {employee.name} for {month_date.strftime('%B %Y')}.")
                return redirect('payroll:index')

            # Calculate final pay - no overtime rate anymore
            final_pay = base_salary + bonus - deduction

            # Generate unique payroll ID
            payroll_id = generate_unique_payroll_id()

            # Create the salary payment record
            salary_payment = SalaryPayment.objects.create(
                payroll_id=payroll_id,
                employee=employee,
                month=month_date,
                base_salary=base_salary,
                bonus=bonus,
                deduction=deduction,
                overtime_hours=overtime_hours,
                final_pay=final_pay,
                payment_method=payment_method,
                employment_type=employment_type,
                notes=notes
            )

            # Log the salary payment creation
            log_create_action(
                request=request,
                module='payroll',
                target_model='SalaryPayment',
                target_id=payroll_id,
                target_description=f'Salary payment {payroll_id} for {employee.name}',
                additional_data={
                    'employee_id': employee.emp_id,
                    'employee_name': employee.name,
                    'month': month_date.strftime('%B %Y'),
                    'base_salary': base_salary,
                    'bonus': bonus,
                    'deduction': deduction,
                    'overtime_hours': overtime_hours,
                    'final_pay': final_pay,
                    'payment_method': payment_method,
                    'employment_type': employment_type,
                    'notes': notes
                },
                financial_impact=-final_pay  # Negative because it's an expense
            )

            messages.success(request, f"Salary payment record created successfully for {employee.name} for {month_date.strftime('%B %Y')}.")
            return redirect('payroll:index')

        except Exception as e:
            messages.error(request, f"Error creating salary payment record: {str(e)}")

    context = {
        'employees': employees,
        'current_month': date.today().strftime('%Y-%m'),
    }
    return render(request, 'payroll/salary_form.html', context)

@login_required
@module_permission_required(module='payroll', required_level='edit')
def process_payment(request, pk):
    """
    Process a salary payment
    """
    payment = get_object_or_404(SalaryPayment, pk=pk)

    if payment.payment_status == 'paid':
        messages.error(request, "This payment has already been processed.")
        return redirect('payroll:index')

    # Update payment status
    payment.payment_status = 'paid'
    payment.payment_date = timezone.now()
    payment.save()

    # Update employee's due salary
    employee = payment.employee
    employee.due -= payment.final_pay
    employee.save()

    # Update funds
    meta = MetaData.objects.last()
    if not meta:
        meta = MetaData.objects.create(funds=0)
    meta.funds -= payment.final_pay
    meta.save()

    messages.success(request, f"Payment of {payment.final_pay}៛ successfully processed for {payment.employee.name}")
    return redirect('payroll:index')

@login_required
@module_permission_required(module='payroll', required_level='view')
def view_payment(request, pk):
    """
    View a salary payment
    """
    payment = get_object_or_404(SalaryPayment, pk=pk)

    context = {
        'payment': payment,
    }
    return render(request, 'payroll/view_payment.html', context)

@login_required
@module_permission_required(module='payroll', required_level='edit')
def edit_payment(request, pk):
    """
    Edit a salary payment
    """
    payment = get_object_or_404(SalaryPayment, pk=pk)

    if payment.payment_status == 'paid':
        messages.error(request, "Paid payments cannot be edited.")
        return redirect('payroll:index')

    if request.method == "POST":
        try:
            payment.base_salary = int(request.POST.get("base_salary"))
            payment.bonus = int(request.POST.get("bonus") or 0)
            payment.deduction = int(request.POST.get("deduction") or 0)
            payment.overtime_hours = int(request.POST.get("overtime_hours") or 0)
            payment.payment_method = request.POST.get("payment_method")
            payment.employment_type = request.POST.get("employment_type")
            payment.notes = request.POST.get("notes")

            # Calculate final pay - no overtime rate anymore
            payment.final_pay = payment.base_salary + payment.bonus - payment.deduction

            payment.save()

            # Log the salary payment edit
            log_edit_action(
                request=request,
                module='payroll',
                target_model='SalaryPayment',
                target_id=payment.payroll_id,
                target_description=f'Salary payment {payment.payroll_id} for {payment.employee.name}',
                additional_data={
                    'employee_id': payment.employee.emp_id,
                    'employee_name': payment.employee.name,
                    'month': payment.month.strftime('%B %Y'),
                    'base_salary': payment.base_salary,
                    'bonus': payment.bonus,
                    'deduction': payment.deduction,
                    'overtime_hours': payment.overtime_hours,
                    'final_pay': payment.final_pay,
                    'payment_method': payment.payment_method,
                    'employment_type': payment.employment_type,
                    'notes': payment.notes
                }
            )

            messages.success(request, f"Salary payment record updated successfully for {payment.employee.name}.")
            return redirect('payroll:index')

        except Exception as e:
            messages.error(request, f"Error updating salary payment record: {str(e)}")

    context = {
        'payment': payment,
    }
    return render(request, 'payroll/edit_payment.html', context)

@login_required
@module_permission_required(module='payroll', required_level='full')
def delete_payment(request, pk):
    """
    Delete a salary payment record and reverse its financial impact
    """
    payment = get_object_or_404(SalaryPayment, pk=pk)

    # Store information for success message
    payroll_id = payment.payroll_id
    employee_name = payment.employee.name
    final_pay = payment.final_pay
    payment_period = payment.month.strftime("%B %Y")
    payment_status = payment.get_payment_status_display()
    processed_by = "System"  # Since we don't track who processed it

    try:
        # Log the salary payment deletion before deleting
        log_delete_action(
            request=request,
            module='payroll',
            target_model='SalaryPayment',
            target_id=payroll_id,
            target_description=f'Salary payment {payroll_id} for {employee_name}',
            additional_data={
                'employee_name': employee_name,
                'payment_period': payment_period,
                'final_pay': final_pay,
                'payment_status': payment_status,
                'base_salary': payment.base_salary,
                'bonus': payment.bonus,
                'deduction': payment.deduction,
                'overtime_hours': payment.overtime_hours,
                'payment_method': payment.payment_method,
                'employment_type': payment.employment_type
            },
            financial_impact=final_pay if payment.payment_status == 'paid' else 0
        )

        # Only reverse financial impact if the payment was already processed (paid)
        if payment.payment_status == 'paid':
            # Reverse the financial impact by adding the amount back to funds
            meta = MetaData.objects.last()
            if meta:
                meta.funds += final_pay
                meta.save()

            # Also reverse the employee's due salary adjustment
            employee = payment.employee
            employee.due += final_pay
            employee.save()

        # Delete the payment
        payment.delete()

        # Format amount for display
        formatted_amount = f"{final_pay:,}៛"

        # Create success message based on payment status
        if payment.payment_status == 'paid':
            success_message = (
                f"Salary payment {payroll_id} deleted successfully! "
                f"Amount {formatted_amount} for {employee_name} ({payment_period}) has been added back to gym funds "
                f"and employee due salary has been adjusted. "
                f"(Status: {payment_status})"
            )
        else:
            success_message = (
                f"Salary payment {payroll_id} deleted successfully! "
                f"Pending payment of {formatted_amount} for {employee_name} ({payment_period}) has been removed. "
                f"(Status: {payment_status})"
            )

        messages.success(request, success_message)

    except Exception as e:
        messages.error(request, f"Error deleting salary payment {payroll_id}: {str(e)}")

    return redirect('payroll:index')

@login_required
def employee_salary(request):
    """
    View salary history for the logged-in employee
    """
    employee = request.user
    payments = SalaryPayment.objects.filter(employee=employee).order_by('-month')

    context = {
        'employee': employee,
        'payments': payments,
    }
    return render(request, 'payroll/employee_salary.html', context)

@login_required
@module_permission_required(module='payroll', required_level='view')
def print_slip(request, pk):
    """
    Print salary slip
    """
    payment = get_object_or_404(SalaryPayment, pk=pk)

    # Get the default template or the first available template
    template = SlipTemplate.objects.filter(is_default=True).first()
    if not template:
        template = SlipTemplate.objects.first()

    # If a specific template is requested
    template_id = request.GET.get('template')
    if template_id:
        requested_template = SlipTemplate.objects.filter(id=template_id).first()
        if requested_template:
            template = requested_template

    context = {
        'payment': payment,
        'template': template,
        'all_templates': SlipTemplate.objects.all(),
    }
    return render(request, 'payroll/print_slip.html', context)

@login_required
@module_permission_required(module='payroll', required_level='view')
def template_list(request):
    """
    List all salary slip templates
    """
    templates = SlipTemplate.objects.all().order_by('-is_default', 'name')

    context = {
        'templates': templates,
    }
    return render(request, 'payroll/template_list.html', context)

@login_required
@module_permission_required(module='payroll', required_level='edit')
def create_template(request):
    """
    Create a new salary slip template
    """
    if request.method == "POST":
        try:
            name = request.POST.get("name")
            language = request.POST.get("language")
            header_text = request.POST.get("header_text")
            subheader_text = request.POST.get("subheader_text")
            footer_text = request.POST.get("footer_text")
            background_color = request.POST.get("background_color")
            text_color = request.POST.get("text_color")
            accent_color = request.POST.get("accent_color")
            is_default = request.POST.get("is_default") == "on"
            show_company_info = request.POST.get("show_company_info") == "on"
            show_signatures = request.POST.get("show_signatures") == "on"
            custom_css = request.POST.get("custom_css")

            template = SlipTemplate.objects.create(
                name=name,
                language=language,
                header_text=header_text,
                subheader_text=subheader_text,
                footer_text=footer_text,
                background_color=background_color,
                text_color=text_color,
                accent_color=accent_color,
                is_default=is_default,
                show_company_info=show_company_info,
                show_signatures=show_signatures,
                custom_css=custom_css
            )

            # Handle logo upload
            if 'company_logo' in request.FILES:
                template.company_logo = request.FILES['company_logo']
                template.save()

            messages.success(request, f"Template '{name}' created successfully.")
            return redirect('payroll:template_list')

        except Exception as e:
            messages.error(request, f"Error creating template: {str(e)}")

    context = {
        'languages': SlipTemplate.LANGUAGE_CHOICES,
    }
    return render(request, 'payroll/create_template.html', context)

@login_required
@module_permission_required(module='payroll', required_level='edit')
def edit_template(request, pk):
    """
    Edit a salary slip template
    """
    template = get_object_or_404(SlipTemplate, pk=pk)

    if request.method == "POST":
        try:
            template.name = request.POST.get("name")
            template.language = request.POST.get("language")
            template.header_text = request.POST.get("header_text")
            template.subheader_text = request.POST.get("subheader_text")
            template.footer_text = request.POST.get("footer_text")
            template.background_color = request.POST.get("background_color")
            template.text_color = request.POST.get("text_color")
            template.accent_color = request.POST.get("accent_color")
            template.is_default = request.POST.get("is_default") == "on"
            template.show_company_info = request.POST.get("show_company_info") == "on"
            template.show_signatures = request.POST.get("show_signatures") == "on"
            template.custom_css = request.POST.get("custom_css")

            # Handle logo upload
            if 'company_logo' in request.FILES:
                template.company_logo = request.FILES['company_logo']

            template.save()

            messages.success(request, f"Template '{template.name}' updated successfully.")
            return redirect('payroll:template_list')

        except Exception as e:
            messages.error(request, f"Error updating template: {str(e)}")

    context = {
        'template': template,
        'languages': SlipTemplate.LANGUAGE_CHOICES,
    }
    return render(request, 'payroll/edit_template.html', context)

@login_required
@module_permission_required(module='payroll', required_level='full')
def delete_template(request, pk):
    """
    Delete a salary slip template
    """
    template = get_object_or_404(SlipTemplate, pk=pk)

    # Don't delete the last template
    if SlipTemplate.objects.count() <= 1:
        messages.error(request, "Cannot delete the last template.")
        return redirect('payroll:template_list')

    name = template.name
    template.delete()

    messages.success(request, f"Template '{name}' deleted successfully.")
    return redirect('payroll:template_list')

@login_required
@module_permission_required(module='payroll', required_level='view')
def preview_template(request, pk):
    """
    Preview a salary slip template
    """
    template = get_object_or_404(SlipTemplate, pk=pk)

    # Get a sample payment for preview
    sample_payment = SalaryPayment.objects.filter(payment_status='paid').first()
    if not sample_payment:
        # Create a dummy payment for preview if no real payments exist
        sample_payment = {
            'payroll_id': 'SAMPLE-001',
            'employee': {
                'name': 'John Doe',
                'emp_id': 'EMP-001',
                'role': 'manager'
            },
            'month': date.today(),
            'payment_date': date.today(),
            'base_salary': 1000000,
            'bonus': 100000,
            'deduction': 50000,
            'overtime_hours': 10,
            'final_pay': 1050000,
            'payment_method': 'cash',
            'employment_type': 'full_time',
            'notes': 'This is a sample payment for template preview.',
            'get_payment_method_display': lambda: 'Cash',
            'get_employment_type_display': lambda: 'Full-time'
        }

    context = {
        'template': template,
        'payment': sample_payment,
        'is_preview': True
    }
    return render(request, 'payroll/preview_template.html', context)

@login_required
@module_permission_required(module='payroll', required_level='edit')
def bulk_actions(request):
    """
    Handle bulk actions for salary payments
    """
    if request.method != "POST":
        return redirect('payroll:index')

    action = request.POST.get('bulk_action')
    selected_ids = request.POST.getlist('selected_payments')

    if not selected_ids:
        messages.error(request, "No payments selected.")
        return redirect('payroll:index')

    payments = SalaryPayment.objects.filter(id__in=selected_ids)

    if action == 'process':
        # Process only pending payments
        pending_payments = payments.filter(payment_status='pending')
        processed_count = 0

        for payment in pending_payments:
            # Update payment status
            payment.payment_status = 'paid'
            payment.payment_date = timezone.now()
            payment.save()

            # Update employee's due salary
            employee = payment.employee
            employee.due -= payment.final_pay
            employee.save()

            # Update funds
            meta = MetaData.objects.last()
            if not meta:
                meta = MetaData.objects.create(funds=0)
            meta.funds -= payment.final_pay
            meta.save()

            processed_count += 1

        if processed_count > 0:
            messages.success(request, f"Successfully processed {processed_count} payments.")
        else:
            messages.warning(request, "No pending payments were found in your selection.")

    elif action == 'delete':
        # Delete only pending payments
        pending_payments = payments.filter(payment_status='pending')
        deleted_count = pending_payments.count()
        pending_payments.delete()

        if deleted_count > 0:
            messages.success(request, f"Successfully deleted {deleted_count} pending payments.")
        else:
            messages.warning(request, "No pending payments were found in your selection. Paid payments cannot be deleted.")

    elif action == 'print':
        # Only print paid payments
        paid_payments = payments.filter(payment_status='paid')

        if paid_payments.count() > 0:
            context = {
                'payments': paid_payments,
            }
            return render(request, 'payroll/bulk_print.html', context)
        else:
            messages.warning(request, "No paid payments were found in your selection.")

    return redirect('payroll:index')
