{% extends "../base.html" %}
{% load custom_filters %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/responsive-tables.css' %}">
<link rel="stylesheet" href="{% static 'css/user-management.css' %}">
<style>
/* Member List Specific Styles */
.member-list-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: calc(100vh - 120px);
}

.member-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
}

.member-photo-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.filter-section {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-expired { background-color: #fef2f2; color: #b91c1c; }
.status-critical { background-color: #fef2f2; color: #dc2626; }
.status-warning { background-color: #fffbeb; color: #d97706; }
.status-good { background-color: #f0fdf4; color: #16a34a; }
.status-active { background-color: #f0fdf4; color: #16a34a; }
.status-inactive { background-color: #f9fafb; color: #6b7280; }
.status-paid { background-color: #f0fdf4; color: #16a34a; }
.status-pending { background-color: #fffbeb; color: #d97706; }

/* Mobile card view */
.member-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    border: 1px solid #e5e7eb;
}

.member-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .desktop-table { display: none; }
    .mobile-cards { display: block; }
}

@media (min-width: 769px) {
    .desktop-table { display: block; }
    .mobile-cards { display: none; }
}
</style>
{% endblock %}

{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 sm:ml-64 member-list-container">
  <div class="componentWrapper">
    <!-- Header with navigation -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
      <div>
        <h3 class="text-2xl font-bold text-gray-900">{% trans "Member Management" %}</h3>
        <p class="text-gray-600 mt-1">{% trans "Manage gym members and their memberships" %}</p>
      </div>

        <div class="flex justify-between items-center mb-4">
            <a href="{% url 'member:create_member' %}" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-200">
                <i class="fas fa-plus mr-2"></i> Add New Member
            </a>
        </div>
    </div>
    <!-- Search and Filter Section -->
    <div class="filter-section p-6">
      <div class="flex flex-col lg:flex-row gap-4">
        <!-- Search Box -->
        <div class="flex-grow max-w-md">
          <label for="memberSearch" class="block text-sm font-medium text-gray-700 mb-2">{% trans "Search Members" %}</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <i class="fa-solid fa-search text-gray-500"></i>
            </div>
            <input type="text" id="memberSearch" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5" placeholder="{% trans 'Search by name, ID, phone, or status...' %}">
          </div>
        </div>

        <!-- Package Filter -->
        <div class="min-w-0 flex-shrink-0">
          <label for="packageFilter" class="block text-sm font-medium text-gray-700 mb-2">{% trans "Package" %}</label>
          <select id="packageFilter" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
            <option value="">{% trans "All Packages" %}</option>
            {% for package in packages %}
            <option value="{{ package.name }}">{{ package.name }}</option>
            {% endfor %}
          </select>
        </div>

        <!-- Status Filter -->
        <div class="min-w-0 flex-shrink-0">
          <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">{% trans "Status" %}</label>
          <select id="statusFilter" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
            <option value="">{% trans "All Status" %}</option>
            <option value="active">{% trans "Active" %}</option>
            <option value="inactive">{% trans "Inactive" %}</option>
          </select>
        </div>

        <!-- Expiration Filter -->
        <div class="min-w-0 flex-shrink-0">
          <label for="expirationFilter" class="block text-sm font-medium text-gray-700 mb-2">{% trans "Expiration" %}</label>
          <select id="expirationFilter" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
            <option value="">{% trans "All" %}</option>
            <option value="expired">{% trans "Expired" %}</option>
            <option value="critical">{% trans "Critical (≤7 days)" %}</option>
            <option value="warning">{% trans "Warning (≤30 days)" %}</option>
            <option value="good">{% trans "Good (>30 days)" %}</option>
          </select>
        </div>

        <!-- Payment Status Filter -->
        <div class="min-w-0 flex-shrink-0">
          <label for="paymentFilter" class="block text-sm font-medium text-gray-700 mb-2">{% trans "Payment" %}</label>
          <select id="paymentFilter" class="bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5">
            <option value="">{% trans "All Payments" %}</option>
            <option value="paid">{% trans "Paid" %}</option>
            <option value="pending">{% trans "Pending" %}</option>
          </select>
        </div>

        <!-- Clear Filters Button -->
        <div class="flex items-end">
          <button id="clearFilters" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2.5 px-4 rounded-lg transition-colors">
            <i class="fa-solid fa-times mr-2"></i>{% trans "Clear" %}
          </button>
        </div>
      </div>

      <!-- Results Summary -->
      <div class="mt-4 flex justify-between items-center text-sm text-gray-600">
        <span id="resultsCount">{% trans "Showing" %} <span id="visibleCount">{{ page_obj.start_index }}</span> {% trans "to" %} <span id="endCount">{{ page_obj.end_index }}</span> {% trans "of" %} <span id="totalCount">{{ page_obj.paginator.count }}</span> {% trans "members" %}</span>
        <div class="flex items-center space-x-4">
          <button id="viewToggle" class="lg:hidden bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-xs font-medium">
            <i class="fa-solid fa-th-large mr-1"></i>{% trans "Card View" %}
          </button>
        </div>
      </div>
    </div>

    <!-- Expiring Memberships Alert -->
    {% with expiring_members=members|dictsortreversed:"days_remaining" %}
    {% with soon_expiring=expiring_members|slice:":5" %}
    {% with has_expiring=False %}
    {% for member in soon_expiring %}
      {% if member.is_expiring_soon %}
        {% with has_expiring=True %}{% endwith %}
      {% endif %}
    {% endfor %}

    {% if has_expiring %}
    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 rounded-lg shadow-sm">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800">{% trans "Memberships Expiring Soon" %}</h3>
          <div class="mt-2 text-sm text-yellow-700">
            <ul class="list-disc pl-5 space-y-1">
              {% for member in soon_expiring %}
                {% if member.is_expiring_soon %}
                <li>
                  <strong>{{ member.name }}</strong> ({{ member.member_id }}) -
                  {% if member.expiration_status == "expires_today" %}
                    <span class="text-orange-600 font-medium">
                      {% trans "Expires today" %}
                    </span>
                  {% elif member.expiration_status == "critical" %}
                    <span class="text-red-600 font-medium">
                      {{ member.days_remaining }} {% trans "day" %}{{ member.days_remaining|pluralize }} {% trans "left" %}
                    </span>
                  {% else %}
                    <span class="text-yellow-600 font-medium">
                      {{ member.days_remaining }} {% trans "day" %}{{ member.days_remaining|pluralize }} {% trans "left" %}
                    </span>
                  {% endif %}
                  <a href="{% url 'member:edit' member.id %}" class="text-blue-600 hover:underline ml-2">{% trans "View" %}</a>
                </li>
                {% endif %}
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
    {% endwith %}
    {% endwith %}
    {% endwith %}

    <!-- Desktop Table View -->
    <div class="desktop-table bg-white rounded-lg shadow-sm overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">{% trans "Members List" %}</h3>
      </div>
      <div class="overflow-x-auto">
        <table class="w-full responsive-table">
          <thead class="bg-blue-700 text-white">
            <tr>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Photo" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Member ID" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Name" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Contact" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Package" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Start Date" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "End Date" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Expiration" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Status" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Payment" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Due/Advanced" %}</th>
              <th scope="col" class="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider">{% trans "Actions" %}</th>
            </tr>
          </thead>
          <tbody id="memberTableBody" class="bg-white divide-y divide-gray-200">
            {% for member in members %}
            <tr class="member-row hover:bg-gray-50 transition-colors"
                data-name="{{ member.name|lower }}"
                data-id="{{ member.member_id|lower }}"
                data-phone="{{ member.contact }}"
                data-package="{{ member.package.name }}"
                data-status="{% if member.status %}active{% else %}inactive{% endif %}"
                data-expiration="{{ member.expiration_status }}"
                data-payment="{{ member.payment_status }}">

              <!-- Photo -->
              <td class="px-4 py-4 whitespace-nowrap">
                {% if member.photo %}
                <img src="{{ member.photo.url }}" alt="{{ member.name }}" class="member-photo">
                {% else %}
                <div class="member-photo-placeholder">
                  {{ member.name|first|upper }}
                </div>
                {% endif %}
              </td>

              <!-- Member ID -->
              <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {{ member.member_id }}
              </td>

              <!-- Name -->
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ member.name }}</div>
                <div class="text-sm text-gray-500">{{ member.gender|capfirst }}</div>
              </td>

              <!-- Contact -->
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ member.contact }}
              </td>

              <!-- Package -->
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ member.package.name }}</div>
                <div class="text-xs text-gray-500">({{ member.package.duration }} {% trans "months" %})</div>
              </td>

              <!-- Start Date -->
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ member.start_date }}
              </td>

              <!-- End Date -->
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ member.end_date }}
              </td>

              <!-- Expiration Status -->
              <td class="px-4 py-4 whitespace-nowrap">
                {% if member.expiration_status == "expired" %}
                <span class="status-badge status-expired">
                  {% trans "Expired" %} {{ member.days_remaining|abs_val }} {% trans "day" %}{{ member.days_remaining|abs_val|pluralize }} {% trans "ago" %}
                </span>
                {% elif member.expiration_status == "expires_today" %}
                <span class="status-badge status-critical">
                  {% trans "Expires today" %}
                </span>
                {% elif member.expiration_status == "critical" %}
                <span class="status-badge status-critical">
                  {{ member.days_remaining }} {% trans "day" %}{{ member.days_remaining|pluralize }} {% trans "left" %}
                </span>
                {% elif member.expiration_status == "warning" %}
                <span class="status-badge status-warning">
                  {{ member.days_remaining }} {% trans "day" %}{{ member.days_remaining|pluralize }} {% trans "left" %}
                </span>
                {% else %}
                <span class="status-badge status-good">
                  {{ member.days_remaining }} {% trans "day" %}{{ member.days_remaining|pluralize }} {% trans "left" %}
                </span>
                {% endif %}
              </td>

              <!-- Status -->
              <td class="px-4 py-4 whitespace-nowrap">
                {% if member.status %}
                <span class="status-badge status-active">{% trans "Active" %}</span>
                {% else %}
                <span class="status-badge status-inactive">{% trans "Inactive" %}</span>
                {% endif %}
              </td>

              <!-- Payment Status -->
              <td class="px-4 py-4 whitespace-nowrap">
                {% if member.payment_status == "pending" %}
                <span class="status-badge status-pending">{% trans "Pending" %}</span>
                {% elif member.payment_status == "paid" %}
                <span class="status-badge status-paid">{% trans "Paid" %}</span>
                {% endif %}
              </td>

              <!-- Due/Advanced -->
              <td class="px-4 py-4 whitespace-nowrap text-sm">
                {% if member.due_payment < 0 %}
                <span class="text-green-600 font-medium">
                  {{ member.due_payment|abs_val|format_khr }} {% trans "Advanced" %}
                </span>
                {% else %}
                <span class="text-red-600 font-medium">
                  {{ member.due_payment|format_khr }} {% trans "Due" %}
                </span>
                {% endif %}
              </td>

              <!-- Actions -->
              <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                <a href="{% url 'member:edit' member.id %}" class="text-blue-600 hover:text-blue-900 transition-colors">
                  <i class="fa-solid fa-eye mr-1"></i>{% trans "View" %}
                </a>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Mobile Card View -->
    <div class="mobile-cards">
      <div id="memberCardContainer" class="grid grid-cols-1 gap-4">
        {% for member in members %}
        <div class="member-card p-4 member-card-item"
             data-name="{{ member.name|lower }}"
             data-id="{{ member.member_id|lower }}"
             data-phone="{{ member.contact }}"
             data-package="{{ member.package.name }}"
             data-status="{% if member.status %}active{% else %}inactive{% endif %}"
             data-expiration="{{ member.expiration_status }}"
             data-payment="{{ member.payment_status }}">

          <div class="flex items-start space-x-4">
            <!-- Photo -->
            <div class="flex-shrink-0">
              {% if member.photo %}
              <img src="{{ member.photo.url }}" alt="{{ member.name }}" class="w-16 h-16 rounded-full object-cover border-2 border-gray-200">
              {% else %}
              <div class="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-lg">
                {{ member.name|first|upper }}
              </div>
              {% endif %}
            </div>

            <!-- Member Info -->
            <div class="flex-grow min-w-0">
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="text-lg font-semibold text-gray-900 truncate">{{ member.name }}</h4>
                  <p class="text-sm text-gray-600">{{ member.member_id }}</p>
                  <p class="text-sm text-gray-600">{{ member.contact }}</p>
                </div>
                <div class="flex flex-col items-end space-y-1">
                  {% if member.status %}
                  <span class="status-badge status-active">{% trans "Active" %}</span>
                  {% else %}
                  <span class="status-badge status-inactive">{% trans "Inactive" %}</span>
                  {% endif %}

                  {% if member.payment_status == "pending" %}
                  <span class="status-badge status-pending">{% trans "Pending" %}</span>
                  {% elif member.payment_status == "paid" %}
                  <span class="status-badge status-paid">{% trans "Paid" %}</span>
                  {% endif %}
                </div>
              </div>

              <!-- Package Info -->
              <div class="mt-3 grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span class="text-gray-500">{% trans "Package" %}:</span>
                  <div class="font-medium">{{ member.package.name }}</div>
                  <div class="text-xs text-gray-500">({{ member.package.duration }} {% trans "months" %})</div>
                </div>
                <div>
                  <span class="text-gray-500">{% trans "Period" %}:</span>
                  <div class="font-medium">{{ member.start_date }}</div>
                  <div class="text-xs text-gray-500">{% trans "to" %} {{ member.end_date }}</div>
                </div>
              </div>

              <!-- Expiration Status -->
              <div class="mt-3">
                <span class="text-gray-500 text-sm">{% trans "Expiration" %}:</span>
                <div class="mt-1">
                  {% if member.expiration_status == "expired" %}
                  <span class="status-badge status-expired">
                    {% trans "Expired" %} {{ member.days_remaining|abs_val }} {% trans "day" %}{{ member.days_remaining|abs_val|pluralize }} {% trans "ago" %}
                  </span>
                  {% elif member.expiration_status == "expires_today" %}
                  <span class="status-badge status-critical">
                    {% trans "Expires today" %}
                  </span>
                  {% elif member.expiration_status == "critical" %}
                  <span class="status-badge status-critical">
                    {{ member.days_remaining }} {% trans "day" %}{{ member.days_remaining|pluralize }} {% trans "left" %}
                  </span>
                  {% elif member.expiration_status == "warning" %}
                  <span class="status-badge status-warning">
                    {{ member.days_remaining }} {% trans "day" %}{{ member.days_remaining|pluralize }} {% trans "left" %}
                  </span>
                  {% else %}
                  <span class="status-badge status-good">
                    {{ member.days_remaining }} {% trans "day" %}{{ member.days_remaining|pluralize }} {% trans "left" %}
                  </span>
                  {% endif %}
                </div>
              </div>

              <!-- Due/Advanced -->
              <div class="mt-3 flex justify-between items-center">
                <div>
                  <span class="text-gray-500 text-sm">{% trans "Payment" %}:</span>
                  <div class="font-medium">
                    {% if member.due_payment < 0 %}
                    <span class="text-green-600">
                      {{ member.due_payment|abs_val|format_khr }} {% trans "Advanced" %}
                    </span>
                    {% else %}
                    <span class="text-red-600">
                      {{ member.due_payment|format_khr }} {% trans "Due" %}
                    </span>
                    {% endif %}
                  </div>
                </div>
                <a href="{% url 'member:edit' member.id %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                  <i class="fa-solid fa-eye mr-1"></i>{% trans "View" %}
                </a>
              </div>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>

    <!-- No Results Message -->
    <div id="noResults" class="hidden text-center py-12">
      <div class="text-gray-500">
        <i class="fa-solid fa-search text-4xl mb-4"></i>
        <h3 class="text-lg font-medium mb-2">{% trans "No members found" %}</h3>
        <p class="text-sm">{% trans "Try adjusting your search criteria or filters" %}</p>
      </div>
    </div>

    <!-- Enhanced Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="bg-white border-t border-gray-200 rounded-b-lg">
        <!-- Mobile Pagination (Simple Previous/Next) -->
        <div class="flex justify-between items-center px-4 py-3 sm:hidden">
            {% if page_obj.has_previous %}
                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-chevron-left mr-2"></i>{% trans "Previous" %}
                </a>
            {% else %}
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                    <i class="fas fa-chevron-left mr-2"></i>{% trans "Previous" %}
                </span>
            {% endif %}

            <span class="text-sm text-gray-700">
                {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
            </span>

            {% if page_obj.has_next %}
                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    {% trans "Next" %}<i class="fas fa-chevron-right ml-2"></i>
                </a>
            {% else %}
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                    {% trans "Next" %}<i class="fas fa-chevron-right ml-2"></i>
                </span>
            {% endif %}
        </div>

        <!-- Desktop Pagination (Full Controls) -->
        <div class="hidden sm:flex sm:flex-col lg:flex-row lg:items-center lg:justify-between px-6 py-4 space-y-4 lg:space-y-0">
            <!-- Left side: Results info and items per page -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                <div>
                    <p class="text-sm text-gray-700">
                        {% trans "Showing" %} <span class="font-medium">{{ page_obj.start_index }}</span> {% trans "to" %} <span class="font-medium">{{ page_obj.end_index }}</span> {% trans "of" %} <span class="font-medium">{{ page_obj.paginator.count }}</span> {% trans "entries" %}
                    </p>
                </div>
                <div class="flex items-center">
                    <label for="items-per-page" class="text-sm text-gray-700 mr-2">{% trans "Items per page" %}:</label>
                    <select id="items-per-page" name="items_per_page" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500" onchange="changeItemsPerPage()">
                        <option value="5" {% if items_per_page == 5 %}selected{% endif %}>5</option>
                        <option value="10" {% if items_per_page == 10 %}selected{% endif %}>10</option>
                        <option value="25" {% if items_per_page == 25 %}selected{% endif %}>25</option>
                        <option value="50" {% if items_per_page == 50 %}selected{% endif %}>50</option>
                        <option value="100" {% if items_per_page == 100 %}selected{% endif %}>100</option>
                    </select>
                </div>
            </div>

            <!-- Right side: Pagination controls -->
            <div class="flex items-center space-x-2">
                <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                    <!-- First Page -->
                    {% if page_obj.has_previous %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page=1"
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                           title="{% trans 'First page' %}">
                            <span class="sr-only">{% trans "First" %}</span>
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                            <span class="sr-only">{% trans "First" %}</span>
                            <i class="fas fa-angle-double-left"></i>
                        </span>
                    {% endif %}

                    <!-- Previous Page -->
                    {% if page_obj.has_previous %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                           class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">{% trans "Previous" %}</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                            <span class="sr-only">{% trans "Previous" %}</span>
                            <i class="fas fa-chevron-left"></i>
                        </span>
                    {% endif %}

                    <!-- Page Numbers -->
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span aria-current="page" class="relative z-10 inline-flex items-center bg-blue-900 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-900">{{ num }}</span>
                        {% elif num > page_obj.number|add:"-3" and num < page_obj.number|add:"3" %}
                            <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                               class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                        {% elif num == 1 or num == page_obj.paginator.num_pages %}
                            {% if num != page_obj.number %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                   class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                            {% endif %}
                        {% elif num == page_obj.number|add:"-4" or num == page_obj.number|add:"4" %}
                            <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                        {% endif %}
                    {% endfor %}

                    <!-- Next Page -->
                    {% if page_obj.has_next %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                           class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">{% trans "Next" %}</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                            <span class="sr-only">{% trans "Next" %}</span>
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    {% endif %}

                    <!-- Last Page -->
                    {% if page_obj.has_next %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.paginator.num_pages }}"
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                           title="{% trans 'Last page' %}">
                            <span class="sr-only">{% trans "Last" %}</span>
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                            <span class="sr-only">{% trans "Last" %}</span>
                            <i class="fas fa-angle-double-right"></i>
                        </span>
                    {% endif %}
                </nav>

                <!-- Jump to Page -->
                <div class="flex items-center ml-4">
                    <span class="text-sm text-gray-700 mr-2">{% trans "Go to page" %}:</span>
                    <input type="number" id="jump-to-page" min="1" max="{{ page_obj.paginator.num_pages }}" value="{{ page_obj.number }}"
                           class="w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500">
                    <button id="jump-page-btn" class="ml-2 bg-blue-900 text-white px-2 py-1 rounded text-sm hover:bg-blue-800 transition duration-200">{% trans "Go" %}</button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

  </div>
</div>
{% endblock body %}

{% block js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get filter elements
    const memberSearch = document.getElementById('memberSearch');
    const packageFilter = document.getElementById('packageFilter');
    const statusFilter = document.getElementById('statusFilter');
    const expirationFilter = document.getElementById('expirationFilter');
    const paymentFilter = document.getElementById('paymentFilter');
    const clearFilters = document.getElementById('clearFilters');
    const viewToggle = document.getElementById('viewToggle');
    const exportBtn = document.getElementById('exportBtn');

    // Get member elements
    const memberRows = document.querySelectorAll('.member-row');
    const memberCards = document.querySelectorAll('.member-card-item');
    const noResults = document.getElementById('noResults');
    const visibleCount = document.getElementById('visibleCount');
    const totalCount = document.getElementById('totalCount');

    // Set total count
    totalCount.textContent = memberRows.length;

    // Add event listeners
    if (memberSearch) memberSearch.addEventListener('input', filterMembers);
    if (packageFilter) packageFilter.addEventListener('change', filterMembers);
    if (statusFilter) statusFilter.addEventListener('change', filterMembers);
    if (expirationFilter) expirationFilter.addEventListener('change', filterMembers);
    if (paymentFilter) paymentFilter.addEventListener('change', filterMembers);
    if (clearFilters) clearFilters.addEventListener('click', clearAllFilters);
    if (viewToggle) viewToggle.addEventListener('click', toggleView);
    if (exportBtn) exportBtn.addEventListener('click', exportData);

    function filterMembers() {
        if (!memberSearch) return;

        const searchTerm = memberSearch.value.toLowerCase();
        const packageValue = packageFilter ? packageFilter.value : '';
        const statusValue = statusFilter ? statusFilter.value : '';
        const expirationValue = expirationFilter ? expirationFilter.value : '';
        const paymentValue = paymentFilter ? paymentFilter.value : '';

        let visibleRowCount = 0;
        let visibleCardCount = 0;

        // Filter table rows
        memberRows.forEach(row => {
            const name = row.getAttribute('data-name') || '';
            const id = row.getAttribute('data-id') || '';
            const phone = row.getAttribute('data-phone') || '';
            const packageName = row.getAttribute('data-package') || '';
            const status = row.getAttribute('data-status') || '';
            const expiration = row.getAttribute('data-expiration') || '';
            const payment = row.getAttribute('data-payment') || '';

            const matchesSearch = name.includes(searchTerm) ||
                                id.includes(searchTerm) ||
                                phone.includes(searchTerm);
            const matchesPackage = packageValue === '' || packageName === packageValue;
            const matchesStatus = statusValue === '' || status === statusValue;
            const matchesExpiration = expirationValue === '' || expiration === expirationValue;
            const matchesPayment = paymentValue === '' || payment === paymentValue;

            const isVisible = matchesSearch && matchesPackage && matchesStatus &&
                            matchesExpiration && matchesPayment;

            if (isVisible) {
                row.style.display = '';
                visibleRowCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Filter card elements
        memberCards.forEach(card => {
            const name = card.getAttribute('data-name') || '';
            const id = card.getAttribute('data-id') || '';
            const phone = card.getAttribute('data-phone') || '';
            const packageName = card.getAttribute('data-package') || '';
            const status = card.getAttribute('data-status') || '';
            const expiration = card.getAttribute('data-expiration') || '';
            const payment = card.getAttribute('data-payment') || '';

            const matchesSearch = name.includes(searchTerm) ||
                                id.includes(searchTerm) ||
                                phone.includes(searchTerm);
            const matchesPackage = packageValue === '' || packageName === packageValue;
            const matchesStatus = statusValue === '' || status === statusValue;
            const matchesExpiration = expirationValue === '' || expiration === expirationValue;
            const matchesPayment = paymentValue === '' || payment === paymentValue;

            const isVisible = matchesSearch && matchesPackage && matchesStatus &&
                            matchesExpiration && matchesPayment;

            if (isVisible) {
                card.style.display = '';
                visibleCardCount++;
            } else {
                card.style.display = 'none';
            }
        });

        // Update visible count
        const currentVisible = Math.max(visibleRowCount, visibleCardCount);
        visibleCount.textContent = currentVisible;

        // Show/hide no results message
        if (currentVisible === 0) {
            noResults.classList.remove('hidden');
        } else {
            noResults.classList.add('hidden');
        }
    }

    function clearAllFilters() {
        if (memberSearch) memberSearch.value = '';
        if (packageFilter) packageFilter.value = '';
        if (statusFilter) statusFilter.value = '';
        if (expirationFilter) expirationFilter.value = '';
        if (paymentFilter) paymentFilter.value = '';

        // Show all members
        memberRows.forEach(row => row.style.display = '');
        memberCards.forEach(card => card.style.display = '');

        // Update count
        visibleCount.textContent = memberRows.length;
        noResults.classList.add('hidden');
    }

    function toggleView() {
        const desktopTable = document.querySelector('.desktop-table');
        const mobileCards = document.querySelector('.mobile-cards');
        const icon = viewToggle.querySelector('i');
        const text = viewToggle.querySelector('span') || viewToggle;

        if (desktopTable.style.display === 'none') {
            // Switch to table view
            desktopTable.style.display = 'block';
            mobileCards.style.display = 'none';
            icon.className = 'fa-solid fa-th-large mr-1';
            text.innerHTML = '<i class="fa-solid fa-th-large mr-1"></i>{% trans "Card View" %}';
        } else {
            // Switch to card view
            desktopTable.style.display = 'none';
            mobileCards.style.display = 'block';
            icon.className = 'fa-solid fa-table mr-1';
            text.innerHTML = '<i class="fa-solid fa-table mr-1"></i>{% trans "Table View" %}';
        }
    }

    function exportData() {
        // Get visible members data
        const visibleMembers = [];
        memberRows.forEach(row => {
            if (row.style.display !== 'none') {
                const cells = row.querySelectorAll('td');
                if (cells.length > 0) {
                    visibleMembers.push({
                        id: cells[1].textContent.trim(),
                        name: cells[2].querySelector('div').textContent.trim(),
                        contact: cells[3].textContent.trim(),
                        package: cells[4].querySelector('div').textContent.trim(),
                        startDate: cells[5].textContent.trim(),
                        endDate: cells[6].textContent.trim(),
                        expiration: cells[7].textContent.trim(),
                        status: cells[8].textContent.trim(),
                        payment: cells[9].textContent.trim(),
                        due: cells[10].textContent.trim()
                    });
                }
            }
        });

        if (visibleMembers.length === 0) {
            alert('{% trans "No data to export" %}');
            return;
        }

        // Create CSV content
        const headers = [
            '{% trans "Member ID" %}',
            '{% trans "Name" %}',
            '{% trans "Contact" %}',
            '{% trans "Package" %}',
            '{% trans "Start Date" %}',
            '{% trans "End Date" %}',
            '{% trans "Expiration" %}',
            '{% trans "Status" %}',
            '{% trans "Payment" %}',
            '{% trans "Due/Advanced" %}'
        ];

        let csvContent = headers.join(',') + '\n';

        visibleMembers.forEach(member => {
            const row = [
                member.id,
                `"${member.name}"`,
                member.contact,
                `"${member.package}"`,
                member.startDate,
                member.endDate,
                `"${member.expiration}"`,
                member.status,
                member.payment,
                `"${member.due}"`
            ];
            csvContent += row.join(',') + '\n';
        });

        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'members_list.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Initialize filters
    filterMembers();

    // Pagination functionality
    window.changeItemsPerPage = function() {
        const itemsPerPage = document.getElementById('items-per-page').value;
        const url = new URL(window.location);
        url.searchParams.set('items_per_page', itemsPerPage);
        url.searchParams.delete('page'); // Reset to first page
        window.location.href = url.toString();
    };

    // Jump to page functionality
    const jumpToPageInput = document.getElementById('jump-to-page');
    const jumpPageBtn = document.getElementById('jump-page-btn');

    if (jumpToPageInput && jumpPageBtn) {
        jumpPageBtn.addEventListener('click', function() {
            const pageNumber = parseInt(jumpToPageInput.value);
            const maxPage = parseInt(jumpToPageInput.getAttribute('max'));

            if (pageNumber >= 1 && pageNumber <= maxPage) {
                const url = new URL(window.location);
                url.searchParams.set('page', pageNumber);
                window.location.href = url.toString();
            } else {
                alert(`Please enter a valid page number between 1 and ${maxPage}`);
                jumpToPageInput.value = "{{ page_obj.number }}";
            }
        });

        jumpToPageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                jumpPageBtn.click();
            }
        });
    }
});
</script>
{% endblock js %}