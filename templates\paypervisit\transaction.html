{% extends 'base.html' %}

{% load static %}
{% load permission_tags %}
{% load currency_filters %}
{% load i18n %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/paypervisit.css' %}">
<style>
    /* Responsive table improvements */
    @media (max-width: 768px) {
        .paypervisit-table th:nth-child(4),
        .paypervisit-table td:nth-child(4) {
            display: none; /* Hide Date & Time column on mobile */
        }

        .paypervisit-table th:nth-child(5),
        .paypervisit-table td:nth-child(5) {
            display: none; /* Hide Cashier column on mobile */
        }

        .delete-paypervisit-btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.625rem !important;
        }

        .delete-paypervisit-btn i {
            margin-right: 0 !important;
        }

        .delete-paypervisit-btn .btn-text {
            display: none;
        }
    }

    @media (max-width: 640px) {
        .paypervisit-table th:nth-child(2),
        .paypervisit-table td:nth-child(2) {
            display: none; /* Hide Visitors column on small mobile */
        }
    }

    /* Touch-friendly improvements */
    .touch-manipulation {
        touch-action: manipulation;
    }
</style>
{% endblock %}

{% block body %}
<!-- Hidden CSRF token for JavaScript -->
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 space-y-2 sm:space-y-0">
            <div class="flex items-center">
                <a href="{% url 'paypervisit:index' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3 transition duration-200">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h2 class="text-2xl font-bold text-gray-800">{% trans "Pay-per-visit Transaction History" %}</h2>
            </div>
        </div>

        <!-- Filter Form -->
        <div class="bg-white p-4 rounded-lg shadow-md mb-6">
            <h3 class="text-lg font-semibold mb-4 text-gray-800 flex items-center">
                <i class="fas fa-filter mr-2 text-blue-600"></i>
                {% trans "Filter Transactions" %}
            </h3>
            <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Date Filters -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-alt mr-1"></i>
                        {% trans "From Date" %}
                    </label>
                    <input type="date" name="start_date" class="border border-gray-300 w-full p-3 leading-tight bg-slate-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200"
                           value="{{ start_date }}">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-calendar-alt mr-1"></i>
                        {% trans "To Date" %}
                    </label>
                    <input type="date" name="end_date" class="border border-gray-300 w-full p-3 leading-tight bg-slate-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200"
                           value="{{ end_date }}">
                </div>

                <!-- Payment Method Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-wallet mr-1"></i>
                        {% trans "Payment Method" %}
                    </label>
                    <select name="payment_method" class="border border-gray-300 w-full p-3 leading-tight bg-slate-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200">
                        <option value="">{% trans "All Payment Methods" %}</option>
                        {% for value, display in payment_methods %}
                        <option value="{{ value }}" {% if payment_method_filter == value %}selected{% endif %}>{{ display }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Cashier Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user-circle mr-1"></i>
                        {% trans "Cashier" %}
                    </label>
                    <select name="cashier" class="border border-gray-300 w-full p-3 leading-tight bg-slate-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200">
                        <option value="">{% trans "All Cashiers" %}</option>
                        {% for cashier in cashiers %}
                        <option value="{{ cashier.id }}" {% if cashier_filter == cashier.id|stringformat:"s" %}selected{% endif %}>{{ cashier.username }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Filter Buttons -->
                <div class="md:col-span-2 lg:col-span-4 flex flex-wrap items-center justify-end gap-2 mt-2">
                    <button type="submit" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded-lg transition duration-200 touch-manipulation shadow-md">
                        <i class="fas fa-filter mr-2"></i> {% trans "Apply Filters" %}
                    </button>
                    {% if filter_active %}
                    <a href="{% url 'paypervisit:transaction' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition duration-200 touch-manipulation shadow-md">
                        <i class="fas fa-redo mr-2"></i> {% trans "Reset" %}
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>

        <!-- Summary Cards -->
        {% if transactions %}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-gradient-to-r from-blue-800 to-blue-900 text-white p-4 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="rounded-full bg-blue-700 p-3 mr-4">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-blue-200 font-medium">{% trans "Total Visitors" %}</p>
                        <p class="text-2xl font-bold">{{ total_people|floatformat:0 }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-gradient-to-r from-green-700 to-green-800 text-white p-4 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="rounded-full bg-green-600 p-3 mr-4">
                        <i class="fas fa-money-bill-wave text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-green-200 font-medium">{% trans "Total Revenue" %}</p>
                        <p class="text-2xl font-bold">{{ total_amount|format_khr }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-gradient-to-r from-purple-700 to-purple-800 text-white p-4 rounded-lg shadow-md">
                <div class="flex items-center">
                    <div class="rounded-full bg-purple-600 p-3 mr-4">
                        <i class="fas fa-receipt text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-purple-200 font-medium">{% trans "Transactions" %}</p>
                        <p class="text-2xl font-bold">{{ transaction_count|floatformat:0 }}</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Transactions Table -->
        <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left paypervisit-table">
                    <thead class="text-xs uppercase bg-blue-900 text-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3">Transaction ID</th>
                            <th scope="col" class="px-6 py-3">Visitors</th>
                            <th scope="col" class="px-6 py-3">Amount</th>
                            <th scope="col" class="px-6 py-3">Date & Time</th>
                            <th scope="col" class="px-6 py-3">Cashier</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in page_obj %}
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4 font-medium">{{ transaction.trxId }}</td>
                            <td class="px-6 py-4">
                                <span class="flex items-center">
                                    <i class="fas fa-users text-blue-700 mr-2"></i>
                                    {{ transaction.num_people }}
                                </span>
                            </td>
                            <td class="px-6 py-4 font-semibold">{{ transaction.amount|format_khr }}</td>
                            <td class="px-6 py-4">{{ transaction.date|date:"M d, Y H:i" }}</td>
                            <td class="px-6 py-4">
                                <span class="flex items-center">
                                    <i class="fas fa-user-circle text-gray-500 mr-2"></i>
                                    {{ transaction.received_by.username }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="{% url 'paypervisit:print_receipt' transaction.id %}" class="text-blue-600 hover:text-blue-800 flex items-center">
                                        <i class="fas fa-print mr-1"></i> Print
                                    </a>
                                    {% has_permission user 'paypervisit' 'full' as can_delete_paypervisit %}
                                    {% if can_delete_paypervisit %}
                                    <button
                                        type="button"
                                        class="bg-red-600 hover:bg-red-700 text-white font-medium py-1 px-3 rounded text-xs transition duration-200 delete-paypervisit-btn"
                                        data-transaction-id="{{ transaction.id }}"
                                        data-transaction-trx="{{ transaction.trxId }}"
                                        data-transaction-amount="{{ transaction.amount|format_khr }}"
                                        data-transaction-people="{{ transaction.num_people }}"
                                        data-transaction-cashier="{{ transaction.received_by.username|default:'Unknown' }}"
                                        title="Delete Transaction">
                                        <i class="fas fa-trash mr-1"></i><span class="btn-text">Delete</span>
                                    </button>
                                    {% else %}
                                    <span class="text-gray-400 text-xs">No Access</span>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border-b">
                            <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                <i class="fas fa-receipt text-5xl mb-3 block opacity-30"></i>
                                {% if filter_active %}
                                <p>No pay-per-visit payments found for the selected filters.</p>
                                {% else %}
                                <p>No pay-per-visit payments recorded yet.</p>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

        <!-- Enhanced Pagination -->
        <div class="bg-white border-t border-gray-200">
            <!-- Mobile Pagination (Simple Previous/Next) -->
            <div class="flex justify-between items-center px-4 py-3 sm:hidden">
                {% if page_obj.has_previous %}
                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <i class="fas fa-chevron-left mr-2"></i>Previous
                    </a>
                {% else %}
                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                        <i class="fas fa-chevron-left mr-2"></i>Previous
                    </span>
                {% endif %}

                <span class="text-sm text-gray-700">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>

                {% if page_obj.has_next %}
                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next<i class="fas fa-chevron-right ml-2"></i>
                    </a>
                {% else %}
                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                        Next<i class="fas fa-chevron-right ml-2"></i>
                    </span>
                {% endif %}
            </div>

            <!-- Desktop Pagination (Full Controls) -->
            <div class="hidden sm:flex sm:flex-col lg:flex-row lg:items-center lg:justify-between px-6 py-4 space-y-4 lg:space-y-0">
                <!-- Left side: Results info and items per page -->
                <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> entries
                        </p>
                    </div>
                    <div class="flex items-center">
                        <label for="items-per-page" class="text-sm text-gray-700 mr-2">Items per page:</label>
                        <select id="items-per-page" name="items_per_page" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500" onchange="changeItemsPerPage()">
                            <option value="5" {% if items_per_page == 5 %}selected{% endif %}>5</option>
                            <option value="10" {% if items_per_page == 10 %}selected{% endif %}>10</option>
                            <option value="25" {% if items_per_page == 25 %}selected{% endif %}>25</option>
                            <option value="50" {% if items_per_page == 50 %}selected{% endif %}>50</option>
                            <option value="100" {% if items_per_page == 100 %}selected{% endif %}>100</option>
                        </select>
                    </div>
                </div>

                <!-- Right side: Pagination controls -->
                {% if page_obj.has_other_pages %}
                <div class="flex items-center space-x-2">
                    <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                        <!-- First Page -->
                        {% if page_obj.has_previous %}
                            <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page=1"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                               title="First page">
                                <span class="sr-only">First</span>
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        {% else %}
                            <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                <span class="sr-only">First</span>
                                <i class="fas fa-angle-double-left"></i>
                            </span>
                        {% endif %}

                        <!-- Previous Page -->
                        {% if page_obj.has_previous %}
                            <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                               class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Previous</span>
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        {% else %}
                            <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                <span class="sr-only">Previous</span>
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        {% endif %}

                        <!-- Page Numbers -->
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <span aria-current="page" class="relative z-10 inline-flex items-center bg-blue-900 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-900">{{ num }}</span>
                            {% elif num > page_obj.number|add:"-3" and num < page_obj.number|add:"3" %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                   class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                            {% elif num == 1 or num == page_obj.paginator.num_pages %}
                                {% if num != page_obj.number %}
                                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                       class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                {% endif %}
                            {% elif num == page_obj.number|add:"-4" or num == page_obj.number|add:"4" %}
                                <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                            {% endif %}
                        {% endfor %}

                        <!-- Next Page -->
                        {% if page_obj.has_next %}
                            <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                               class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Next</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        {% else %}
                            <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                <span class="sr-only">Next</span>
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        {% endif %}

                        <!-- Last Page -->
                        {% if page_obj.has_next %}
                            <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.paginator.num_pages }}"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                               title="Last page">
                                <span class="sr-only">Last</span>
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        {% else %}
                            <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                <span class="sr-only">Last</span>
                                <i class="fas fa-angle-double-right"></i>
                            </span>
                        {% endif %}
                    </nav>

                    <!-- Jump to Page -->
                    <div class="flex items-center ml-4">
                        <span class="text-sm text-gray-700 mr-2">Go to page:</span>
                        <input type="number" id="jump-to-page" min="1" max="{{ page_obj.paginator.num_pages }}" value="{{ page_obj.number }}"
                               class="w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500">
                        <button id="jump-page-btn" class="ml-2 bg-blue-900 text-white px-2 py-1 rounded text-sm hover:bg-blue-800 transition duration-200">Go</button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Items per page functionality
        const itemsPerPageSelect = document.getElementById('items-per-page');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', function() {
                changeItemsPerPage();
            });
        }

        // Jump to page functionality
        const jumpToPageInput = document.getElementById('jump-to-page');
        const jumpPageBtn = document.getElementById('jump-page-btn');

        if (jumpToPageInput && jumpPageBtn) {
            jumpPageBtn.addEventListener('click', function() {
                const pageNum = parseInt(jumpToPageInput.value);
                const maxPage = parseInt(jumpToPageInput.getAttribute('max'));

                if (pageNum && pageNum > 0 && pageNum <= maxPage) {
                    // Build the URL with the current query parameters
                    let url = new URL(window.location.href);
                    let params = new URLSearchParams(url.search);

                    // Update or add the page parameter
                    params.set('page', pageNum);

                    // Redirect to the new URL
                    window.location.href = `${url.pathname}?${params.toString()}`;
                } else {
                    alert(`Please enter a valid page number between 1 and ${maxPage}`);
                    jumpToPageInput.value = "{{ page_obj.number }}";
                }
            });

            // Allow Enter key to trigger jump
            jumpToPageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    jumpPageBtn.click();
                }
            });
        }

        // Delete pay-per-visit transaction functionality with confirmation
        document.querySelectorAll('.delete-paypervisit-btn').forEach(button => {
            button.addEventListener('click', function() {
                const transactionId = this.getAttribute('data-transaction-id');
                const transactionTrx = this.getAttribute('data-transaction-trx');
                const transactionAmount = this.getAttribute('data-transaction-amount');
                const transactionPeople = this.getAttribute('data-transaction-people');
                const cashier = this.getAttribute('data-transaction-cashier');

                // Use the visitor/visitors text based on the number of people
                const visitorText = transactionPeople == 1 ? "visitor" : "visitors";

                // Create confirmation dialog
                const confirmMessage = `Are you sure you want to delete this pay-per-visit transaction?\n\n` +
                    `Transaction ID: ${transactionTrx}\n` +
                    `Amount: ${transactionAmount}\n` +
                    `Visitors: ${transactionPeople} ${visitorText}\n` +
                    `Processed by: ${cashier}\n\n` +
                    `This action cannot be undone. The transaction amount will be deducted from gym funds.`;

                if (confirm(confirmMessage)) {
                    // Create a form and submit it to delete the transaction
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/paypervisit/transaction/delete/${transactionId}/`;

                    // Add CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Submit the form
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Function to change items per page
    function changeItemsPerPage() {
        const itemsPerPage = document.getElementById('items-per-page').value;

        // Build the URL with current query parameters
        let url = new URL(window.location.href);
        let params = new URLSearchParams(url.search);

        // Update items per page and reset to page 1
        params.set('items_per_page', itemsPerPage);
        params.set('page', '1');

        // Redirect to the new URL
        window.location.href = `${url.pathname}?${params.toString()}`;
    }
</script>
{% endblock %}
