from django.contrib import admin
from .models import Settings

class SettingsAdmin(admin.ModelAdmin):
    """Admin interface for Settings model"""

    fieldsets = (
        ('General Settings', {
            'fields': ('gym_name', 'contact_email', 'contact_phone', 'address'),
        }),
        ('Product Settings', {
            'fields': ('auto_deactivate_out_of_stock', 'auto_reactivate_in_stock', 'default_items_per_page'),
        }),

        ('UI Settings', {
            'fields': (
                'notification_success_color', 'notification_error_color',
                'notification_warning_color', 'notification_info_color',
                'notification_text_color',
            ),
        }),
        ('Payment Settings', {
            'fields': ('default_currency', 'exchange_rate_usd_to_khr'),
        }),
        ('System Settings', {
            'fields': ('last_backup_date', 'last_data_cleanup', 'funds', 'last_checked'),
            'classes': ('collapse',),
        }),
        ('Metadata', {
            'fields': ('last_updated', 'created_at'),
            'classes': ('collapse',),
        }),
    )

    readonly_fields = ('last_updated', 'created_at')

    def has_add_permission(self, request):
        """Prevent creating new settings objects"""
        # Only allow adding if no settings object exists
        return not Settings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        """Prevent deleting the settings object"""
        return False

# Register the Settings model with the custom admin
admin.site.register(Settings, SettingsAdmin)
