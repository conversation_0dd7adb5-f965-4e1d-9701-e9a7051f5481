from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db import connection
from django.utils import timezone
from django.http import HttpResponse, FileResponse, JsonResponse
from django.conf import settings as django_settings
from django.core.management import call_command
from core.decorators import module_permission_required
from .models import Settings, RolePermission, MODULE_CHOICES, PERMISSION_LEVELS
from .utils import (
    get_settings, update_settings, mark_data_cleanup_complete, mark_backup_complete,
    reset_role_permissions, get_user_permissions, get_default_permissions, get_users_with_role, log_permission_change
)
from .cache_manager import PermissionCacheManager
from user.models import User, MetaData, ROLE_CHOICES
import os
import datetime
import subprocess
import json

@login_required
@module_permission_required(module='settings', required_level='view')
def dashboard(request):
    """Main settings dashboard view"""
    settings = get_settings()

    context = {
        'settings': settings,
        'title': 'Settings Dashboard',
        'breadcrumbs': [
            {'title': 'Dashboard', 'url': '/adminDashboard/', 'icon': 'tachometer-alt'},
            {'title': 'Settings', 'url': '#', 'icon': 'cog'},
        ]
    }
    return render(request, 'settings/dashboard.html', context)

@login_required
@module_permission_required(module='settings', required_level='edit')
def general(request):
    """General settings view"""
    settings = get_settings()

    if request.method == 'POST':
        try:
            # Get form data
            gym_name = request.POST.get('gym_name')
            contact_email = request.POST.get('contact_email')
            contact_phone = request.POST.get('contact_phone')
            address = request.POST.get('address')

            # Update settings
            update_settings({
                'gym_name': gym_name,
                'contact_email': contact_email,
                'contact_phone': contact_phone,
                'address': address,
            })

            messages.success(request, "General settings updated successfully")
            return redirect('settings:general')
        except Exception as e:
            messages.error(request, f"Error updating settings: {str(e)}")

    context = {
        'settings': settings,
        'title': 'General Settings',
        'breadcrumbs': [
            {'title': 'Dashboard', 'url': '/adminDashboard/', 'icon': 'tachometer-alt'},
            {'title': 'Settings', 'url': '/settings/', 'icon': 'cog'},
            {'title': 'General', 'url': '#', 'icon': 'building'},
        ]
    }
    return render(request, 'settings/general.html', context)

@login_required
@module_permission_required(module='settings', required_level='edit')
def product(request):
    """Product settings view"""
    settings = get_settings()

    if request.method == 'POST':
        try:
            # Get form data
            auto_deactivate = request.POST.get('auto_deactivate_out_of_stock') == 'on'
            auto_reactivate = request.POST.get('auto_reactivate_in_stock') == 'on'
            items_per_page = int(request.POST.get('default_items_per_page', 10))

            # Update settings
            update_settings({
                'auto_deactivate_out_of_stock': auto_deactivate,
                'auto_reactivate_in_stock': auto_reactivate,
                'default_items_per_page': items_per_page,
            })

            # Also update MetaData for backward compatibility
            try:
                metadata = MetaData.objects.last()
                if metadata:
                    metadata.auto_deactivate_out_of_stock = auto_deactivate
                    metadata.auto_reactivate_in_stock = auto_reactivate
                    metadata.default_items_per_page = items_per_page
                    metadata.save()
            except:
                pass

            messages.success(request, "Product settings updated successfully")
            return redirect('settings:product')
        except Exception as e:
            messages.error(request, f"Error updating settings: {str(e)}")

    context = {
        'settings': settings,
        'title': 'Product Settings',
        'breadcrumbs': [
            {'title': 'Dashboard', 'url': '/adminDashboard/', 'icon': 'tachometer-alt'},
            {'title': 'Settings', 'url': '/settings/', 'icon': 'cog'},
            {'title': 'Product', 'url': '#', 'icon': 'box'},
        ]
    }
    return render(request, 'settings/product.html', context)



@login_required
@module_permission_required(module='settings', required_level='edit')
def currency(request):
    """Currency settings view"""
    settings = get_settings()

    if request.method == 'POST':
        try:
            # Get form data
            exchange_rate = request.POST.get('exchange_rate_usd_to_khr')

            # Check if reset to default was requested
            if 'reset_to_default' in request.POST:
                exchange_rate = 4000
            else:
                # Validate exchange rate
                try:
                    exchange_rate = int(exchange_rate)
                    if exchange_rate <= 0:
                        raise ValueError("Exchange rate must be greater than zero")
                except ValueError:
                    messages.error(request, "Exchange rate must be a valid positive number")
                    return redirect('settings:currency')

            # Update settings
            update_settings({
                'exchange_rate_usd_to_khr': exchange_rate,
            })

            messages.success(request, "Currency settings updated successfully")
            return redirect('settings:currency')
        except Exception as e:
            messages.error(request, f"Error updating settings: {str(e)}")

    context = {
        'settings': settings,
        'title': 'Currency Settings',
        'breadcrumbs': [
            {'title': 'Dashboard', 'url': '/adminDashboard/', 'icon': 'tachometer-alt'},
            {'title': 'Settings', 'url': '/settings/', 'icon': 'cog'},
            {'title': 'Currency', 'url': '#', 'icon': 'money-bill-wave'},
        ]
    }
    return render(request, 'settings/currency.html', context)

@login_required
@module_permission_required(module='settings', required_level='edit')
def ui(request):
    """UI settings view"""
    settings = get_settings()

    if request.method == 'POST':
        try:
            # Get form data
            success_color = request.POST.get('notification_success_color', '#4CAF50')
            error_color = request.POST.get('notification_error_color', '#F44336')
            warning_color = request.POST.get('notification_warning_color', '#FF9800')
            info_color = request.POST.get('notification_info_color', '#2196F3')
            text_color = request.POST.get('notification_text_color', '#FFFFFF')

            # Update settings
            update_settings({
                'notification_success_color': success_color,
                'notification_error_color': error_color,
                'notification_warning_color': warning_color,
                'notification_info_color': info_color,
                'notification_text_color': text_color,
            })

            messages.success(request, "UI settings updated successfully")
            return redirect('settings:ui')
        except Exception as e:
            messages.error(request, f"Error updating settings: {str(e)}")

    context = {
        'settings': settings,
        'title': 'UI Settings',
        'breadcrumbs': [
            {'title': 'Dashboard', 'url': '/adminDashboard/', 'icon': 'tachometer-alt'},
            {'title': 'Settings', 'url': '/settings/', 'icon': 'cog'},
            {'title': 'UI', 'url': '#', 'icon': 'palette'},
        ]
    }
    return render(request, 'settings/ui.html', context)

@login_required
@module_permission_required(module='settings', required_level='full')
def system(request):
    """System settings view with data cleaning functionality"""
    settings = get_settings()

    # Initialize counters for display
    data_counts = {}

    # Get counts for all models
    try:
        from members.models import Member, Package
        data_counts['members'] = Member.objects.all().count()
        data_counts['packages'] = Package.objects.all().count()
    except:
        data_counts['members'] = 0
        data_counts['packages'] = 0

    try:
        from payment.models import Payment
        data_counts['payments'] = Payment.objects.all().count()
    except:
        data_counts['payments'] = 0

    try:
        from product.models import Product, Category, Purchase, PurchaseItem, Sale, SaleItem
        data_counts['products'] = Product.objects.all().count()
        data_counts['categories'] = Category.objects.all().count()
        data_counts['purchases'] = Purchase.objects.all().count()
        data_counts['sales'] = Sale.objects.all().count()
    except:
        data_counts['products'] = 0
        data_counts['categories'] = 0
        data_counts['purchases'] = 0
        data_counts['sales'] = 0

    try:
        from paypervisit.models import PayPerVisit
        data_counts['paypervisit'] = PayPerVisit.objects.all().count()
    except:
        data_counts['paypervisit'] = 0

    try:
        from payroll.models import SalaryPayment
        data_counts['salary_payments'] = SalaryPayment.objects.all().count()
    except:
        data_counts['salary_payments'] = 0

    try:
        from billmanagement.models import Bill
        data_counts['bills'] = Bill.objects.all().count()
    except:
        data_counts['bills'] = 0

    try:
        from finance.models import Transaction
        data_counts['finance_transactions'] = Transaction.objects.all().count()
    except:
        data_counts['finance_transactions'] = 0

    # Count non-admin users
    data_counts['users'] = User.objects.filter(role__in=['cashier', 'coach', 'cleaner', 'security']).count()

    # Process POST requests for data cleaning
    if request.method == "POST":
        action = request.POST.get("action")

        # Handle data cleaning actions
        if action == "clean_transaction_table":
            try:
                with connection.cursor() as cursor:
                    # First delete records from transaction_payrollrecord
                    cursor.execute("DELETE FROM transaction_payrollrecord")
                    # Then delete records from transaction_transaction
                    cursor.execute("DELETE FROM transaction_transaction")
                messages.success(request, "Legacy transaction tables cleaned successfully")
            except Exception as e:
                messages.error(request, f"Error cleaning transaction tables: {str(e)}")

        # Clean all data
        elif action == "clean_all":
            try:
                # First clean legacy transaction tables
                with connection.cursor() as cursor:
                    cursor.execute("DELETE FROM transaction_payrollrecord")
                    cursor.execute("DELETE FROM transaction_transaction")

                # Clean all data from all apps
                try:
                    from payment.models import Payment
                    payment_count = Payment.objects.all().count()
                    Payment.objects.all().delete()
                except:
                    payment_count = 0

                try:
                    from members.models import Member, Package
                    member_count = Member.objects.all().count()
                    package_count = Package.objects.all().count()
                    Member.objects.all().delete()
                    Package.objects.all().delete()
                except:
                    member_count = 0
                    package_count = 0

                try:
                    from product.models import Product, Category, Purchase, PurchaseItem, Sale, SaleItem
                    product_count = Product.objects.all().count()
                    category_count = Category.objects.all().count()
                    purchase_count = Purchase.objects.all().count()
                    sale_count = Sale.objects.all().count()

                    SaleItem.objects.all().delete()
                    Sale.objects.all().delete()
                    PurchaseItem.objects.all().delete()
                    Purchase.objects.all().delete()
                    Product.objects.all().delete()
                    Category.objects.all().delete()
                except:
                    product_count = 0
                    category_count = 0
                    purchase_count = 0
                    sale_count = 0

                try:
                    from paypervisit.models import PayPerVisit
                    paypervisit_count = PayPerVisit.objects.all().count()
                    PayPerVisit.objects.all().delete()
                except:
                    paypervisit_count = 0

                try:
                    from payroll.models import SalaryPayment
                    salary_count = SalaryPayment.objects.all().count()
                    SalaryPayment.objects.all().delete()
                except:
                    salary_count = 0

                try:
                    from billmanagement.models import Bill
                    bill_count = Bill.objects.all().count()
                    Bill.objects.all().delete()
                except:
                    bill_count = 0

                # Only delete non-admin users
                user_count = User.objects.filter(role__in=[ 'cashier', 'coach', 'cleaner', 'security']).count()
                User.objects.filter(role__in=[ 'cashier', 'coach', 'cleaner', 'security']).delete()

                # Reset MetaData
                meta = MetaData.objects.last()
                if meta:
                    meta.funds = 0
                    meta.save()

                # Update settings
                mark_data_cleanup_complete()

                messages.success(request, f"All data cleaned successfully: {member_count} members, {package_count} packages, {payment_count} payments, {product_count} products, {purchase_count} purchases, {sale_count} sales, {paypervisit_count} pay-per-visit records, {salary_count} salary payments, {bill_count} bills, and {user_count} users deleted")
            except Exception as e:
                messages.error(request, f"Error cleaning all data: {str(e)}")

        # Handle other specific data cleaning actions
        elif action in ["clean_members", "clean_packages", "clean_payments", "clean_products",
                       "clean_categories", "clean_purchases", "clean_sales", "clean_paypervisit",
                       "clean_salary_payments", "clean_bills", "clean_users"]:
            try:
                if action == "clean_members":
                    from members.models import Member
                    count = Member.objects.all().count()
                    Member.objects.all().delete()
                    messages.success(request, f"All {count} members deleted successfully")

                elif action == "clean_packages":
                    from members.models import Package
                    count = Package.objects.all().count()
                    Package.objects.all().delete()
                    messages.success(request, f"All {count} packages deleted successfully")

                elif action == "clean_payments":
                    from payment.models import Payment
                    count = Payment.objects.all().count()
                    Payment.objects.all().delete()
                    messages.success(request, f"All {count} payments deleted successfully")

                elif action == "clean_products":
                    from product.models import Product
                    count = Product.objects.all().count()
                    Product.objects.all().delete()
                    messages.success(request, f"All {count} products deleted successfully")

                elif action == "clean_categories":
                    from product.models import Category
                    count = Category.objects.all().count()
                    Category.objects.all().delete()
                    messages.success(request, f"All {count} categories deleted successfully")

                elif action == "clean_purchases":
                    from product.models import Purchase, PurchaseItem
                    purchase_count = Purchase.objects.all().count()
                    item_count = PurchaseItem.objects.all().count()
                    PurchaseItem.objects.all().delete()
                    Purchase.objects.all().delete()
                    messages.success(request, f"All {purchase_count} purchases and {item_count} purchase items deleted successfully")

                elif action == "clean_sales":
                    from product.models import Sale, SaleItem
                    sale_count = Sale.objects.all().count()
                    item_count = SaleItem.objects.all().count()
                    SaleItem.objects.all().delete()
                    Sale.objects.all().delete()
                    messages.success(request, f"All {sale_count} sales and {item_count} sale items deleted successfully")

                elif action == "clean_paypervisit":
                    from paypervisit.models import PayPerVisit
                    count = PayPerVisit.objects.all().count()
                    PayPerVisit.objects.all().delete()
                    messages.success(request, f"All {count} pay-per-visit records deleted successfully")

                elif action == "clean_salary_payments":
                    from payroll.models import SalaryPayment
                    count = SalaryPayment.objects.all().count()
                    SalaryPayment.objects.all().delete()
                    messages.success(request, f"All {count} salary payments deleted successfully")

                elif action == "clean_bills":
                    from billmanagement.models import Bill
                    count = Bill.objects.all().count()
                    Bill.objects.all().delete()
                    messages.success(request, f"All {count} bills deleted successfully")

                elif action == "clean_finance":
                    from finance.models import Transaction
                    count = Transaction.objects.all().count()
                    Transaction.objects.all().delete()

                    # Reset MetaData funds
                    meta = MetaData.objects.last()
                    if meta:
                        meta.funds = 0
                        meta.save()

                    messages.success(request, f"All {count} finance transactions deleted successfully")

                elif action == "clean_users":
                    # Only delete non-admin users
                    count = User.objects.filter(role__in=['manager', 'cashier', 'coach', 'cleaner', 'security']).count()
                    User.objects.filter(role__in=['manager', 'cashier', 'coach', 'cleaner', 'security']).delete()
                    messages.success(request, f"All {count} non-admin users deleted successfully")

                # Update last data cleanup timestamp
                mark_data_cleanup_complete()

            except Exception as e:
                messages.error(request, f"Error: {str(e)}")

    context = {
        'settings': settings,
        'data_counts': data_counts,
        'title': 'System Settings',
        'breadcrumbs': [
            {'title': 'Dashboard', 'url': '/adminDashboard/', 'icon': 'tachometer-alt'},
            {'title': 'Settings', 'url': '/settings/', 'icon': 'cog'},
            {'title': 'System', 'url': '#', 'icon': 'server'},
        ]
    }
    return render(request, 'settings/system.html', context)

@login_required
@module_permission_required(module='settings', required_level='full')
def clean_all_data(request):
    """
    View for cleaning all data with one click
    Only accessible to admin users
    """
    if request.method == "POST":
        try:
            # First clean legacy transaction tables
            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM transaction_payrollrecord")
                cursor.execute("DELETE FROM transaction_transaction")

            # Clean all data from all apps
            try:
                from payment.models import Payment
                payment_count = Payment.objects.all().count()
                Payment.objects.all().delete()
            except:
                payment_count = 0

            try:
                from members.models import Member, Package
                member_count = Member.objects.all().count()
                package_count = Package.objects.all().count()
                Member.objects.all().delete()
                Package.objects.all().delete()
            except:
                member_count = 0
                package_count = 0

            try:
                from product.models import Product, Category, Purchase, PurchaseItem, Sale, SaleItem
                product_count = Product.objects.all().count()
                category_count = Category.objects.all().count()
                purchase_count = Purchase.objects.all().count()
                sale_count = Sale.objects.all().count()

                SaleItem.objects.all().delete()
                Sale.objects.all().delete()
                PurchaseItem.objects.all().delete()
                Purchase.objects.all().delete()
                Product.objects.all().delete()
                Category.objects.all().delete()
            except:
                product_count = 0
                category_count = 0
                purchase_count = 0
                sale_count = 0

            try:
                from paypervisit.models import PayPerVisit
                paypervisit_count = PayPerVisit.objects.all().count()
                PayPerVisit.objects.all().delete()
            except:
                paypervisit_count = 0

            try:
                from payroll.models import SalaryPayment
                salary_count = SalaryPayment.objects.all().count()
                SalaryPayment.objects.all().delete()
            except:
                salary_count = 0

            try:
                from billmanagement.models import Bill
                bill_count = Bill.objects.all().count()
                Bill.objects.all().delete()
            except:
                bill_count = 0

            # Only delete non-admin users
            user_count = User.objects.filter(role__in=['manager', 'cashier', 'coach', 'cleaner', 'security']).count()
            User.objects.filter(role__in=['manager', 'cashier', 'coach', 'cleaner', 'security']).delete()

            # Clean finance transactions
            try:
                from finance.models import Transaction
                finance_count = Transaction.objects.all().count()
                Transaction.objects.all().delete()
            except:
                finance_count = 0

            # Reset MetaData
            meta = MetaData.objects.last()
            if meta:
                meta.funds = 0
                meta.save()

            # Update settings
            mark_data_cleanup_complete()

            messages.success(request, f"All data cleaned successfully: {member_count} members, {package_count} packages, {payment_count} payments, {product_count} products, {purchase_count} purchases, {sale_count} sales, {paypervisit_count} pay-per-visit records, {salary_count} salary payments, {bill_count} bills, {finance_count} finance transactions, and {user_count} users deleted")
        except Exception as e:
            messages.error(request, f"Error cleaning all data: {str(e)}")

    # Redirect back to the dashboard
    return redirect('settings:dashboard')

@login_required
@module_permission_required(module='settings', required_level='full')
def backup_database(request):
    """
    Create a backup of the database
    Only accessible to admin users
    """
    if request.method == "POST":
        try:
            # Create backups directory if it doesn't exist
            backup_dir = os.path.join(django_settings.BASE_DIR, 'backups')
            os.makedirs(backup_dir, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"backup_{timestamp}.sql"

            # Call the backup command
            backup_path = call_command('backup_database', filename=timestamp)

            # Mark backup as complete in settings
            mark_backup_complete()

            messages.success(request, f"Database backup created successfully: {filename}")
        except Exception as e:
            messages.error(request, f"Error creating backup: {str(e)}")

    # Redirect to backup list
    return redirect('settings:backup_list')

@login_required
@module_permission_required(module='settings', required_level='view')
def backup_list(request):
    """
    Display a list of available backups
    Only accessible to admin users
    """
    # Get settings
    settings = get_settings()

    # Get list of backup files
    backup_dir = os.path.join(django_settings.BASE_DIR, 'backups')
    os.makedirs(backup_dir, exist_ok=True)

    backups = []
    for file in os.listdir(backup_dir):
        if file.endswith('.sql'):
            file_path = os.path.join(backup_dir, file)
            file_stats = os.stat(file_path)
            file_size = file_stats.st_size / (1024 * 1024)  # Convert to MB
            file_date = datetime.datetime.fromtimestamp(file_stats.st_mtime)

            backups.append({
                'filename': file,
                'size': f"{file_size:.2f} MB",
                'date': file_date,
                'path': file_path
            })

    # Sort backups by date (newest first)
    backups.sort(key=lambda x: x['date'], reverse=True)

    context = {
        'settings': settings,
        'backups': backups,
        'title': 'Database Backups',
        'breadcrumbs': [
            {'title': 'Dashboard', 'url': '/adminDashboard/', 'icon': 'tachometer-alt'},
            {'title': 'Settings', 'url': '/settings/', 'icon': 'cog'},
            {'title': 'System', 'url': '/settings/system/', 'icon': 'server'},
            {'title': 'Backups', 'url': '#', 'icon': 'database'},
        ]
    }
    return render(request, 'settings/backup_list.html', context)

@login_required
@module_permission_required(module='settings', required_level='full')
def restore_database(request, filename):
    """
    Restore the database from a backup file
    Only accessible to admin users
    """
    if request.method == "POST":
        try:
            # Validate filename to prevent directory traversal
            if '..' in filename or '/' in filename:
                messages.error(request, "Invalid backup filename")
                return redirect('settings:backup_list')

            # Get backup file path
            backup_dir = os.path.join(django_settings.BASE_DIR, 'backups')
            backup_path = os.path.join(backup_dir, filename)

            # Check if file exists
            if not os.path.exists(backup_path):
                messages.error(request, f"Backup file not found: {filename}")
                return redirect('settings:backup_list')

            # Call the restore command
            call_command('restore_database', backup_path)

            messages.success(request, f"Database restored successfully from: {filename}")
        except Exception as e:
            messages.error(request, f"Error restoring database: {str(e)}")

    # Redirect to backup list
    return redirect('settings:backup_list')

@login_required
@module_permission_required(module='settings', required_level='full')
def delete_backup(request, filename):
    """
    Delete a backup file
    Only accessible to admin users
    """
    if request.method == "POST":
        try:
            # Validate filename to prevent directory traversal
            if '..' in filename or '/' in filename:
                messages.error(request, "Invalid backup filename")
                return redirect('settings:backup_list')

            # Get backup file path
            backup_dir = os.path.join(django_settings.BASE_DIR, 'backups')
            backup_path = os.path.join(backup_dir, filename)

            # Check if file exists
            if not os.path.exists(backup_path):
                messages.error(request, f"Backup file not found: {filename}")
                return redirect('settings:backup_list')

            # Delete the file
            os.remove(backup_path)

            messages.success(request, f"Backup file deleted: {filename}")
        except Exception as e:
            messages.error(request, f"Error deleting backup: {str(e)}")

    # Redirect to backup list
    return redirect('settings:backup_list')


@login_required
@module_permission_required(module='settings', required_level='view')
def permissions(request):
    """
    View for managing role-based permissions
    """
    # Get all roles
    roles = [role[0] for role in ROLE_CHOICES]

    # Get all modules
    modules = MODULE_CHOICES

    # Get all permission levels
    permission_levels = PERMISSION_LEVELS

    # Get current permissions for each role
    role_permissions = {}
    for role in roles:
        # Get permissions for this role
        permissions = RolePermission.get_role_permissions(role)

        # Convert to a dictionary for easier access in the template
        permissions_dict = {}
        for permission in permissions:
            permissions_dict[permission.module] = permission.permission_level

        role_permissions[role] = permissions_dict

    # Get default permissions for reference
    default_permissions = get_default_permissions()

    context = {
        'title': 'Permissions Management',
        'roles': roles,
        'modules': modules,
        'permission_levels': permission_levels,
        'role_permissions': role_permissions,
        'default_permissions': default_permissions,
        'breadcrumbs': [
            {'title': 'Dashboard', 'url': '/adminDashboard/', 'icon': 'tachometer-alt'},
            {'title': 'Settings', 'url': '/settings/', 'icon': 'cog'},
            {'title': 'Permissions', 'url': '#', 'icon': 'user-shield'},
        ]
    }
    return render(request, 'settings/permissions.html', context)


@login_required
@module_permission_required(module='settings', required_level='edit')
def update_permissions(request):
    """
    Update permissions for a role with real-time notifications
    """
    if request.method == 'POST':
        try:
            # Get the role from the form
            role = request.POST.get('role')

            # Validate role
            if role not in [r[0] for r in ROLE_CHOICES]:
                messages.error(request, f"Invalid role: {role}")
                return redirect('settings:permissions')

            # Get all modules
            modules = [module[0] for module in MODULE_CHOICES]

            # Track changes for notifications
            changes = {}
            affected_users = get_users_with_role(role)

            # Update permissions for each module
            for module in modules:
                # Get the permission level from the form
                permission_level = request.POST.get(f"{role}_{module}")

                # Validate permission level
                if permission_level not in [level[0] for level in PERMISSION_LEVELS]:
                    messages.error(request, f"Invalid permission level for {module}: {permission_level}")
                    continue

                # Get current permission level for comparison
                try:
                    current_permission = RolePermission.objects.get(role=role, module=module)
                    old_level = current_permission.permission_level
                except RolePermission.DoesNotExist:
                    old_level = 'none'

                # Only track changes if the permission level actually changed
                if old_level != permission_level:
                    changes[module] = {
                        'old': old_level,
                        'new': permission_level
                    }

                    # Log the permission change
                    log_permission_change(
                        user=None,
                        role=role,
                        module=module,
                        old_level=old_level,
                        new_level=permission_level,
                        changed_by=request.user
                    )

                # Update or create the permission
                RolePermission.objects.update_or_create(
                    role=role,
                    module=module,
                    defaults={'permission_level': permission_level}
                )

            # Send real-time notifications if there were changes
            if changes:
                PermissionCacheManager.notify_permission_change(
                    role=role,
                    changes=changes,
                    affected_users=affected_users
                )

            messages.success(request, f"Permissions updated for role: {role}")
        except Exception as e:
            messages.error(request, f"Error updating permissions: {str(e)}")

    return redirect('settings:permissions')


@login_required
@module_permission_required(module='settings', required_level='edit')
def reset_role_permissions_view(request, role):
    """
    Reset permissions for a specific role to their defaults
    """
    try:
        # Validate role
        if role not in [r[0] for r in ROLE_CHOICES]:
            messages.error(request, f"Invalid role: {role}")
            return redirect('settings:permissions')

        # Reset permissions for this role
        count = reset_role_permissions(role)

        messages.success(request, f"Reset {count} permissions for role: {role}")
    except Exception as e:
        messages.error(request, f"Error resetting permissions: {str(e)}")

    return redirect('settings:permissions')


@login_required
@module_permission_required(module='settings', required_level='edit')
def reset_all_permissions(request):
    """
    Reset all permissions to their defaults
    """
    try:
        # Reset all permissions
        count = reset_role_permissions()

        messages.success(request, f"Reset {count} permissions for all roles")
    except Exception as e:
        messages.error(request, f"Error resetting permissions: {str(e)}")

    return redirect('settings:permissions')


@login_required
@module_permission_required(module='settings', required_level='full')
def fix_permissions(request):
    """
    Fix permissions issues by ensuring all roles have the correct permissions.
    This is a utility view to help fix permission problems.
    """
    try:
        # Get all roles
        roles = [role[0] for role in ROLE_CHOICES]

        # Get all modules
        modules = [module[0] for module in MODULE_CHOICES]

        # Get default permissions
        from settings.utils import get_default_permissions
        default_permissions = get_default_permissions()

        # Track changes for reporting
        created_count = 0
        updated_count = 0

        # Process each role
        for role in roles:
            # Skip admin role as it always has full access
            if role == 'admin':
                continue

            # Get default permissions for this role
            if role in default_permissions:
                role_defaults = default_permissions[role]

                # Process each module
                for module in modules:
                    # Get current permission
                    try:
                        permission = RolePermission.objects.get(role=role, module=module)
                        current_level = permission.permission_level
                    except RolePermission.DoesNotExist:
                        permission = None
                        current_level = 'none'

                    # Get default permission level
                    default_level = role_defaults.get(module, 'none')

                    # If permission doesn't exist, create it
                    if permission is None:
                        RolePermission.objects.create(
                            role=role,
                            module=module,
                            permission_level=default_level
                        )
                        created_count += 1
                    # If permission exists but doesn't match default, update it
                    elif current_level != default_level:
                        permission.permission_level = default_level
                        permission.save()
                        updated_count += 1

        # Report results
        if created_count > 0 or updated_count > 0:
            messages.success(
                request,
                f"Permissions fixed successfully: {created_count} permissions created, {updated_count} permissions updated."
            )
        else:
            messages.info(request, "All permissions are already correct. No changes were made.")

    except Exception as e:
        messages.error(request, f"Error fixing permissions: {str(e)}")

    return redirect('settings:permissions')