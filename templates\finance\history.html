{% extends 'base.html' %}
{% load custom_filters %}
{% load permission_tags %}



{% block body %}
<!-- Check permission for delete functionality -->
{% has_permission user 'finance' 'full' as can_delete_transaction %}

<!-- Hidden CSRF token for JavaScript -->
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Transaction History</h3>
            <div class="flex space-x-2">
                <a href="{% url 'finance:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Finance
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white p-4 rounded shadow-md mb-6">
            <form method="get" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- Transaction Type Filter -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Transaction Type</label>
                    <select id="type" name="type" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-slate-100">
                        <option value="">All Types</option>
                        {% for value, label in transaction_types %}
                        <option value="{{ value }}" {% if selected_type == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-slate-100">
                        <option value="">All Statuses</option>
                        {% for value, label in statuses %}
                        <option value="{{ value }}" {% if selected_status == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Start Date Filter -->
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="date" id="start_date" name="start_date" value="{{ start_date|date:'Y-m-d' }}" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-slate-100">
                </div>

                <!-- End Date Filter -->
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="date" id="end_date" name="end_date" value="{{ end_date|date:'Y-m-d' }}" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-slate-100">
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-200">
                        <i class="fas fa-filter mr-2"></i>Apply Filters
                    </button>
                </div>
            </form>
        </div>

        <!-- Transactions Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-4 bg-blue-900 text-white">
                <h4 class="text-lg font-semibold">Transactions</h4>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Transaction ID
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Type
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Staff
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Notes
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for transaction in transactions %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{ transaction.transaction_id }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if transaction.transaction_type == 'deposit' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Deposit
                                </span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    Withdrawal
                                </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ transaction.amount_khr|format_khr }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ transaction.transaction_date|date:"d M Y H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ transaction.staff.name }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if transaction.status == 'completed' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Completed
                                </span>
                                {% elif transaction.status == 'pending' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Pending
                                </span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    Rejected
                                </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                                {{ transaction.notes|default:"-" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex flex-col sm:flex-row gap-2">
                                    {% if transaction.status == 'completed' %}
                                    <a href="{% url 'finance:print_receipt' transaction.id %}" class="text-blue-600 hover:text-blue-900 text-sm">
                                        <i class="fas fa-print mr-1"></i><span class="hidden sm:inline">Print</span>
                                    </a>
                                    {% endif %}

                                    {% if transaction.status == 'pending' and transaction.transaction_type == 'withdrawal' and request.user.role == 'admin' or transaction.status == 'pending' and transaction.transaction_type == 'withdrawal' and request.user.is_manager %}
                                    <button class="approve-btn text-green-600 hover:text-green-900 text-sm" data-id="{{ transaction.id }}">
                                        <i class="fas fa-check mr-1"></i><span class="hidden sm:inline">Approve</span>
                                    </button>
                                    <button class="reject-btn text-red-600 hover:text-red-900 text-sm" data-id="{{ transaction.id }}">
                                        <i class="fas fa-times mr-1"></i><span class="hidden sm:inline">Reject</span>
                                    </button>
                                    {% endif %}

                                    {% if can_delete_transaction %}
                                    <button type="button"
                                            class="delete-transaction-btn text-red-600 hover:text-red-800 text-sm font-medium transition duration-200"
                                            data-transaction-id="{{ transaction.id }}"
                                            data-transaction-ref="{{ transaction.transaction_id }}"
                                            data-transaction-type="{{ transaction.get_transaction_type_display }}"
                                            data-amount="{{ transaction.amount_khr|format_khr }}"
                                            data-date="{{ transaction.transaction_date|date:'d M Y H:i' }}"
                                            data-staff="{{ transaction.staff.name|default:'Unknown' }}"
                                            data-status="{{ transaction.get_status_display }}">
                                        <i class="fas fa-trash mr-1"></i><span class="hidden sm:inline">Delete</span>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500">
                                No transactions found
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Enhanced Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="bg-white border-t border-gray-200 rounded-b-lg">
                <!-- Mobile Pagination (Simple Previous/Next) -->
                <div class="flex justify-between items-center px-4 py-3 sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-chevron-left mr-2"></i>Previous
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            <i class="fas fa-chevron-left mr-2"></i>Previous
                        </span>
                    {% endif %}

                    <span class="text-sm text-gray-700">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>

                    {% if page_obj.has_next %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next<i class="fas fa-chevron-right ml-2"></i>
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            Next<i class="fas fa-chevron-right ml-2"></i>
                        </span>
                    {% endif %}
                </div>

                <!-- Desktop Pagination (Full Controls) -->
                <div class="hidden sm:flex sm:flex-col lg:flex-row lg:items-center lg:justify-between px-6 py-4 space-y-4 lg:space-y-0">
                    <!-- Left side: Results info and items per page -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> entries
                            </p>
                        </div>
                        <div class="flex items-center">
                            <label for="items-per-page" class="text-sm text-gray-700 mr-2">Items per page:</label>
                            <select id="items-per-page" name="items_per_page" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500" onchange="changeItemsPerPage()">
                                <option value="5" {% if items_per_page == 5 %}selected{% endif %}>5</option>
                                <option value="10" {% if items_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="25" {% if items_per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if items_per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if items_per_page == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>
                    </div>

                    <!-- Right side: Pagination controls -->
                    <div class="flex items-center space-x-2">
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <!-- First Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page=1"
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="First page">
                                    <span class="sr-only">First</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">First</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </span>
                            {% endif %}

                            <!-- Previous Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            {% endif %}

                            <!-- Page Numbers -->
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span aria-current="page" class="relative z-10 inline-flex items-center bg-blue-900 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-900">{{ num }}</span>
                                {% elif num > page_obj.number|add:"-3" and num < page_obj.number|add:"3" %}
                                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                       class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                {% elif num == 1 or num == page_obj.paginator.num_pages %}
                                    {% if num != page_obj.number %}
                                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                           class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                    {% endif %}
                                {% elif num == page_obj.number|add:"-4" or num == page_obj.number|add:"4" %}
                                    <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                                {% endif %}
                            {% endfor %}

                            <!-- Next Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            {% endif %}

                            <!-- Last Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.paginator.num_pages }}"
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="Last page">
                                    <span class="sr-only">Last</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Last</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </span>
                            {% endif %}
                        </nav>

                        <!-- Jump to Page -->
                        <div class="flex items-center ml-4">
                            <span class="text-sm text-gray-700 mr-2">Go to page:</span>
                            <input type="number" id="jump-to-page" min="1" max="{{ page_obj.paginator.num_pages }}" value="{{ page_obj.number }}"
                                   class="w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500">
                            <button id="jump-page-btn" class="ml-2 bg-blue-900 text-white px-2 py-1 rounded text-sm hover:bg-blue-800 transition duration-200">Go</button>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Pagination functionality
        window.changeItemsPerPage = function() {
            const itemsPerPage = document.getElementById('items-per-page').value;
            const url = new URL(window.location);
            url.searchParams.set('items_per_page', itemsPerPage);
            url.searchParams.delete('page'); // Reset to first page
            window.location.href = url.toString();
        };

        // Jump to page functionality
        const jumpToPageInput = document.getElementById('jump-to-page');
        const jumpPageBtn = document.getElementById('jump-page-btn');

        if (jumpToPageInput && jumpPageBtn) {
            jumpPageBtn.addEventListener('click', function() {
                const pageNumber = parseInt(jumpToPageInput.value);
                const maxPage = parseInt(jumpToPageInput.getAttribute('max'));

                if (pageNumber >= 1 && pageNumber <= maxPage) {
                    const url = new URL(window.location);
                    url.searchParams.set('page', pageNumber);
                    window.location.href = url.toString();
                } else {
                    alert(`Please enter a valid page number between 1 and ${maxPage}`);
                    jumpToPageInput.value = "{{ page_obj.number }}";
                }
            });

            jumpToPageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    jumpPageBtn.click();
                }
            });
        }

        // Approve transaction
        document.querySelectorAll('.approve-btn').forEach(button => {
            button.addEventListener('click', function() {
                const transactionId = this.getAttribute('data-id');
                if (confirm('Are you sure you want to approve this transaction?')) {
                    fetch(`/finance/api/approve-transaction/${transactionId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken'),
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showNotification('success', data.message);
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showNotification('error', data.message);
                        }
                    })
                    .catch(error => {
                        showNotification('error', 'An error occurred while processing your request.');
                    });
                }
            });
        });

        // Reject transaction
        document.querySelectorAll('.reject-btn').forEach(button => {
            button.addEventListener('click', function() {
                const transactionId = this.getAttribute('data-id');
                if (confirm('Are you sure you want to reject this transaction?')) {
                    fetch(`/finance/api/reject-transaction/${transactionId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken'),
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showNotification('success', data.message);
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showNotification('error', data.message);
                        }
                    })
                    .catch(error => {
                        showNotification('error', 'An error occurred while processing your request.');
                    });
                }
            });
        });

        // Delete transaction functionality with confirmation
        document.querySelectorAll('.delete-transaction-btn').forEach(button => {
            button.addEventListener('click', function() {
                const transactionId = this.getAttribute('data-transaction-id');
                const transactionRef = this.getAttribute('data-transaction-ref');
                const transactionType = this.getAttribute('data-transaction-type');
                const amount = this.getAttribute('data-amount');
                const date = this.getAttribute('data-date');
                const staff = this.getAttribute('data-staff');
                const status = this.getAttribute('data-status');

                // Create confirmation dialog
                const confirmDialog = confirm(
                    `⚠️ DELETE TRANSACTION CONFIRMATION ⚠️\n\n` +
                    `Transaction Details:\n` +
                    `• Transaction ID: ${transactionRef}\n` +
                    `• Type: ${transactionType}\n` +
                    `• Amount: ${amount}\n` +
                    `• Date: ${date}\n` +
                    `• Processed By: ${staff}\n` +
                    `• Status: ${status}\n\n` +
                    `⚠️ FINANCIAL WARNING ⚠️\n` +
                    `${status === 'Completed' ?
                        (transactionType === 'Deposit' ?
                            `This will subtract ${amount} from gym funds.` :
                            `This will add ${amount} back to gym funds.`) :
                        `This will remove the pending transaction record.`}\n\n` +
                    `This action cannot be undone!\n\n` +
                    `Are you sure you want to delete this transaction?`
                );

                if (confirmDialog) {
                    // Create a form and submit it
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/finance/delete/${transactionId}/`;

                    // Add CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Add to body and submit
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}
