{% extends 'base.html' %}
{% load custom_filters %}
{% load permission_tags %}



{% block body %}
<!-- Check permission for delete functionality -->
{% has_permission user 'payroll' 'full' as can_delete_salary_payment %}

<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Add Button -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Salary Payments</h3>
            <div class="flex space-x-2">
                <a href="{% url 'payroll:create_salary' %}" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-plus mr-2"></i> Create Salary
                </a>

            </div>
        </div>

        <!-- Salary Payments List -->
        <div class="listSection bg-white p-4 rounded shadow-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold">Payment History</h3>
                <div class="text-right">
                    <p class="font-bold">Total Paid: {{ total_paid|format_khr }}</p>
                </div>
            </div>

            <!-- Filter Form -->
            <form method="get" class="mb-4 grid grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium mb-1">Employee</label>
                    <select name="employee" class="border w-full p-2 leading-tight bg-slate-100">
                        <option value="">All Employees</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}" {% if employee_filter == employee.id|stringformat:"s" %}selected{% endif %}>{{ employee.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">Month</label>
                    <input type="month" name="month" class="border w-full p-2 leading-tight bg-slate-100"
                           value="{{ month_filter }}">
                </div>
                <div>
                    <label class="block text-sm font-medium mb-1">Status</label>
                    <select name="status" class="border w-full p-2 leading-tight bg-slate-100">
                        <option value="">All Statuses</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                        <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>Paid</option>
                    </select>
                </div>
                <div class="col-span-3">
                    <button type="submit" class="bg-blue-900 text-white font-bold py-2 px-4">Filter</button>
                    <a href="{% url 'payroll:index' %}" class="bg-gray-500 text-white font-bold py-2 px-4 ml-2">Reset</a>
                </div>
            </form>

            <!-- Bulk Actions Form -->
            <form id="bulk-actions-form" method="post" action="{% url 'payroll:bulk_actions' %}">
                {% csrf_token %}
                <div class="flex items-center space-x-2 mb-4">
                    <select name="bulk_action" class="border p-2 rounded">
                        <option value="">-- Select Bulk Action --</option>
                        <option value="process">Process Selected Payments</option>
                        <option value="delete">Delete Selected Payments</option>
                        <option value="print">Print Selected Slips</option>
                    </select>
                    <button type="submit" class="bg-blue-900 text-white px-4 py-2 rounded">Apply</button>
                    <button type="button" id="select-all" class="bg-gray-500 text-white px-4 py-2 rounded">Select All</button>
                    <button type="button" id="deselect-all" class="bg-gray-500 text-white px-4 py-2 rounded">Deselect All</button>
                </div>

                <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-2 py-3 w-10">
                                <input type="checkbox" id="select-all-checkbox" class="w-4 h-4"></th>
                            <th scope="col" class="px-6 py-3">Payroll ID</th>
                            <th scope="col" class="px-6 py-3">Employee</th>
                            <th scope="col" class="px-6 py-3">Month</th>
                            <th scope="col" class="px-6 py-3">Base Salary</th>
                            <th scope="col" class="px-6 py-3">Bonus</th>
                            <th scope="col" class="px-6 py-3">Deduction</th>
                            <th scope="col" class="px-6 py-3">Final Pay</th>
                            <th scope="col" class="px-6 py-3">Status</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr class="bg-white border">
                            <td class="px-2 py-4">
                                <input type="checkbox" name="selected_payments" value="{{ payment.id }}" class="payment-checkbox w-4 h-4">
                            </td>
                            <td class="px-6 py-4">{{ payment.payroll_id }}</td>
                            <td class="px-6 py-4">{{ payment.employee.name }}</td>
                            <td class="px-6 py-4">{{ payment.month|date:"M Y" }}</td>
                            <td class="px-6 py-4">{{ payment.base_salary|format_khr }}</td>
                            <td class="px-6 py-4">{{ payment.bonus|format_khr }}</td>
                            <td class="px-6 py-4">{{ payment.deduction|format_khr }}</td>
                            <td class="px-6 py-4">{{ payment.final_pay|format_khr }}</td>
                            <td class="px-6 py-4">
                                {% if payment.payment_status == 'paid' %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
                                {% else %}
                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">Pending</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-col sm:flex-row gap-2">
                                    <a href="{% url 'payroll:view' payment.id %}" class="text-blue-600 hover:underline text-sm">View</a>
                                    {% if payment.payment_status == 'pending' %}
                                    <a href="{% url 'payroll:edit' payment.id %}" class="text-green-600 hover:underline text-sm">Edit</a>
                                    <a href="{% url 'payroll:process' payment.id %}" class="text-purple-600 hover:underline text-sm"
                                       onclick="return confirm('Are you sure you want to process this payment?')">Process</a>
                                    {% if can_delete_salary_payment %}
                                    <button type="button"
                                            class="delete-salary-payment-btn text-red-600 hover:text-red-800 text-sm font-medium transition duration-200"
                                            data-payment-id="{{ payment.id }}"
                                            data-payroll-id="{{ payment.payroll_id }}"
                                            data-employee="{{ payment.employee.name }}"
                                            data-amount="{{ payment.final_pay|format_khr }}"
                                            data-period="{{ payment.month|date:'F Y' }}"
                                            data-status="{{ payment.get_payment_status_display }}">
                                        <i class="fas fa-trash mr-1"></i>Delete
                                    </button>
                                    {% endif %}
                                    {% else %}
                                    <a href="{% url 'payroll:print' payment.id %}" class="text-purple-600 hover:underline text-sm">Print</a>
                                    {% if can_delete_salary_payment %}
                                    <button type="button"
                                            class="delete-salary-payment-btn text-red-600 hover:text-red-800 text-sm font-medium transition duration-200"
                                            data-payment-id="{{ payment.id }}"
                                            data-payroll-id="{{ payment.payroll_id }}"
                                            data-employee="{{ payment.employee.name }}"
                                            data-amount="{{ payment.final_pay|format_khr }}"
                                            data-period="{{ payment.month|date:'F Y' }}"
                                            data-status="{{ payment.get_payment_status_display }}">
                                        <i class="fas fa-trash mr-1"></i>Delete
                                    </button>
                                    {% endif %}
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="10" class="px-6 py-4 text-center">No salary payments found matching the filter criteria.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            </form>

            <!-- Enhanced Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="bg-white border-t border-gray-200 rounded-b-lg">
                <!-- Mobile Pagination (Simple Previous/Next) -->
                <div class="flex justify-between items-center px-4 py-3 sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-chevron-left mr-2"></i>Previous
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            <i class="fas fa-chevron-left mr-2"></i>Previous
                        </span>
                    {% endif %}

                    <span class="text-sm text-gray-700">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>

                    {% if page_obj.has_next %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next<i class="fas fa-chevron-right ml-2"></i>
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            Next<i class="fas fa-chevron-right ml-2"></i>
                        </span>
                    {% endif %}
                </div>

                <!-- Desktop Pagination (Full Controls) -->
                <div class="hidden sm:flex sm:flex-col lg:flex-row lg:items-center lg:justify-between px-6 py-4 space-y-4 lg:space-y-0">
                    <!-- Left side: Results info and items per page -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> entries
                            </p>
                        </div>
                        <div class="flex items-center">
                            <label for="items-per-page" class="text-sm text-gray-700 mr-2">Items per page:</label>
                            <select id="items-per-page" name="items_per_page" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500" onchange="changeItemsPerPage()">
                                <option value="5" {% if items_per_page == 5 %}selected{% endif %}>5</option>
                                <option value="10" {% if items_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="25" {% if items_per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if items_per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if items_per_page == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>
                    </div>

                    <!-- Right side: Pagination controls -->
                    <div class="flex items-center space-x-2">
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <!-- First Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page=1"
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="First page">
                                    <span class="sr-only">First</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">First</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </span>
                            {% endif %}

                            <!-- Previous Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            {% endif %}

                            <!-- Page Numbers -->
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span aria-current="page" class="relative z-10 inline-flex items-center bg-blue-900 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-900">{{ num }}</span>
                                {% elif num > page_obj.number|add:"-3" and num < page_obj.number|add:"3" %}
                                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                       class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                {% elif num == 1 or num == page_obj.paginator.num_pages %}
                                    {% if num != page_obj.number %}
                                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                           class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                    {% endif %}
                                {% elif num == page_obj.number|add:"-4" or num == page_obj.number|add:"4" %}
                                    <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                                {% endif %}
                            {% endfor %}

                            <!-- Next Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            {% endif %}

                            <!-- Last Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.paginator.num_pages }}"
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="Last page">
                                    <span class="sr-only">Last</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Last</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </span>
                            {% endif %}
                        </nav>

                        <!-- Jump to Page -->
                        <div class="flex items-center ml-4">
                            <span class="text-sm text-gray-700 mr-2">Go to page:</span>
                            <input type="number" id="jump-to-page" min="1" max="{{ page_obj.paginator.num_pages }}" value="{{ page_obj.number }}"
                                   class="w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500">
                            <button id="jump-page-btn" class="ml-2 bg-blue-900 text-white px-2 py-1 rounded text-sm hover:bg-blue-800 transition duration-200">Go</button>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Employee salary auto-fill
        const employeeSelect = document.getElementById('employee-select');
        const baseSalaryInput = document.getElementById('base-salary');

        if (employeeSelect) {
            employeeSelect.addEventListener('change', function() {
                const selectedOption = employeeSelect.options[employeeSelect.selectedIndex];
                const salary = selectedOption.getAttribute('data-salary');

                if (salary) {
                    baseSalaryInput.value = salary;
                } else {
                    baseSalaryInput.value = '';
                }
            });
        }

        // Pagination functionality
        window.changeItemsPerPage = function() {
            const itemsPerPage = document.getElementById('items-per-page').value;
            const url = new URL(window.location);
            url.searchParams.set('items_per_page', itemsPerPage);
            url.searchParams.delete('page'); // Reset to first page
            window.location.href = url.toString();
        };

        // Jump to page functionality
        const jumpToPageInput = document.getElementById('jump-to-page');
        const jumpPageBtn = document.getElementById('jump-page-btn');

        if (jumpToPageInput && jumpPageBtn) {
            jumpPageBtn.addEventListener('click', function() {
                const pageNumber = parseInt(jumpToPageInput.value);
                const maxPage = parseInt(jumpToPageInput.getAttribute('max'));

                if (pageNumber >= 1 && pageNumber <= maxPage) {
                    const url = new URL(window.location);
                    url.searchParams.set('page', pageNumber);
                    window.location.href = url.toString();
                } else {
                    alert(`Please enter a valid page number between 1 and ${maxPage}`);
                    jumpToPageInput.value = "{{ page_obj.number }}";
                }
            });

            jumpToPageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    jumpPageBtn.click();
                }
            });
        }

        // Bulk actions checkbox functionality
        const selectAllBtn = document.getElementById('select-all');
        const deselectAllBtn = document.getElementById('deselect-all');
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        const paymentCheckboxes = document.querySelectorAll('.payment-checkbox');

        // Select all button
        if (selectAllBtn) {
            selectAllBtn.addEventListener('click', function() {
                paymentCheckboxes.forEach(checkbox => {
                    checkbox.checked = true;
                });
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = true;
                }
            });
        }

        // Deselect all button
        if (deselectAllBtn) {
            deselectAllBtn.addEventListener('click', function() {
                paymentCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
            });
        }

        // Select all checkbox in table header
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                paymentCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            });
        }

        // Form validation before submission
        const bulkActionsForm = document.getElementById('bulk-actions-form');
        if (bulkActionsForm) {
            bulkActionsForm.addEventListener('submit', function(e) {
                const action = bulkActionsForm.querySelector('select[name="bulk_action"]').value;
                const checkedBoxes = bulkActionsForm.querySelectorAll('input[name="selected_payments"]:checked');

                if (!action) {
                    e.preventDefault();
                    alert('Please select an action to perform.');
                    return false;
                }

                if (checkedBoxes.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one payment record.');
                    return false;
                }

                if (action === 'delete' && !confirm('Are you sure you want to delete the selected payment records? This action cannot be undone.')) {
                    e.preventDefault();
                    return false;
                }

                if (action === 'process' && !confirm('Are you sure you want to process the selected payment records?')) {
                    e.preventDefault();
                    return false;
                }

                return true;
            });
        }

        // Delete salary payment functionality with confirmation
        document.querySelectorAll('.delete-salary-payment-btn').forEach(button => {
            button.addEventListener('click', function() {
                const paymentId = this.getAttribute('data-payment-id');
                const payrollId = this.getAttribute('data-payroll-id');
                const employee = this.getAttribute('data-employee');
                const amount = this.getAttribute('data-amount');
                const period = this.getAttribute('data-period');
                const status = this.getAttribute('data-status');

                // Create confirmation dialog
                const confirmDialog = confirm(
                    `⚠️ DELETE SALARY PAYMENT CONFIRMATION ⚠️\n\n` +
                    `Payment Details:\n` +
                    `• Payroll ID: ${payrollId}\n` +
                    `• Employee: ${employee}\n` +
                    `• Period: ${period}\n` +
                    `• Amount: ${amount}\n` +
                    `• Status: ${status}\n\n` +
                    `⚠️ FINANCIAL WARNING ⚠️\n` +
                    `${status === 'Paid' ?
                        `This will add ${amount} back to gym funds and adjust employee due salary.` :
                        `This will remove the pending payment record.`}\n\n` +
                    `This action cannot be undone!\n\n` +
                    `Are you sure you want to delete this salary payment?`
                );

                if (confirmDialog) {
                    // Create a form and submit it
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/payroll/delete/${paymentId}/`;

                    // Add CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Add to body and submit
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
</script>
{% endblock %}
